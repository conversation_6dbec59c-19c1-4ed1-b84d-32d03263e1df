#!/usr/bin/env python3
"""
测试价格计算功能
"""

import os
import json
from src.price_calculator import PriceCalculator

def test_price_database_creation():
    """测试单价数据库创建"""
    
    print("🧪 测试单价数据库创建")
    print("=" * 60)
    
    calculator = PriceCalculator()
    
    # 检查数据库是否创建
    if os.path.exists(calculator.price_database_path):
        print("✅ 单价数据库文件已创建")
        
        # 检查数据库内容
        database = calculator.price_database
        
        print(f"📊 数据库信息:")
        print(f"- 版本: {database['metadata']['version']}")
        print(f"- 更新时间: {database['metadata']['last_updated']}")
        print(f"- 类别数量: {len(database['categories'])}")
        
        # 显示各类别的资源数量
        for category, cat_data in database['categories'].items():
            resource_count = len(cat_data.get('resources', {}))
            print(f"- {category}: {resource_count} 个资源")
        
        return True
    else:
        print("❌ 单价数据库文件未创建")
        return False

def test_unit_price_lookup():
    """测试单价查询功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试单价查询功能")
    print("=" * 60)
    
    calculator = PriceCalculator()
    
    # 测试查询已存在的资源
    test_cases = [
        ("00010701", "人工", "综合用工三类"),
        ("99030031", "机械", "挖掘机"),
        ("01010001", "材料", "水泥"),
        ("99460004", "其他费用", "其他机具费占人工费"),
        ("UNKNOWN", None, "不存在的资源")
    ]
    
    print("🔍 单价查询测试:")
    
    for resource_code, category, description in test_cases:
        price = calculator.get_unit_price(resource_code, category)
        
        if price > 0:
            print(f"✅ {resource_code} ({description}): {price:.2f} 元")
        else:
            print(f"❌ {resource_code} ({description}): 未找到单价")
    
    return True

def test_price_calculation():
    """测试价格计算功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试价格计算功能")
    print("=" * 60)
    
    calculator = PriceCalculator()
    
    # 创建测试数据
    parent_data = [
        {
            "quota_code": "1-1",
            "quota_name": "人工挖一般土方 一、二类土",
            "comprehensive_price": 0
        }
    ]
    
    child_data = [
        {
            "quota_code": "1-1",
            "resource_code": "00010701",
            "category": "人工",
            "resource_name": "综合用工三类",
            "resource_unit": "工日",
            "consumption": 0.8,
            "unit_price": 0,
            "total_price": 0
        },
        {
            "quota_code": "1-1",
            "resource_code": "99030030",
            "category": "机械",
            "resource_name": "电动打钎机",
            "resource_unit": "台班",
            "consumption": 0.1,
            "unit_price": 0,
            "total_price": 0
        },
        {
            "quota_code": "1-1",
            "resource_code": "99460004",
            "category": "其他费用",
            "resource_name": "其他机具费占人工费",
            "resource_unit": "%",
            "consumption": 5.0,
            "unit_price": 0,
            "total_price": 0
        }
    ]
    
    print("📊 计算前的数据:")
    print("父级数据:")
    for parent in parent_data:
        print(f"  - {parent['quota_code']}: {parent['quota_name']}, 综合单价: {parent['comprehensive_price']}")
    
    print("子级数据:")
    for child in child_data:
        print(f"  - {child['resource_code']}: {child['resource_name']}, 消耗量: {child['consumption']}, 单价: {child['unit_price']}, 合价: {child['total_price']}")
    
    # 执行价格计算
    calculator.calculate_prices(parent_data, child_data)
    
    print(f"\n📊 计算后的数据:")
    print("父级数据:")
    for parent in parent_data:
        print(f"  - {parent['quota_code']}: {parent['quota_name']}, 综合单价: {parent['comprehensive_price']:.2f}")
    
    print("子级数据:")
    for child in child_data:
        print(f"  - {child['resource_code']}: {child['resource_name']}, 消耗量: {child['consumption']}, 单价: {child['unit_price']:.2f}, 合价: {child['total_price']:.2f}")
    
    # 验证计算结果
    expected_results = {
        "00010701": 120.00 * 0.8,  # 人工费
        "99030030": 50.00 * 0.1,   # 机械费
        "99460004": (120.00 * 0.8 + 50.00 * 0.1) * 0.05  # 其他费用（基于人工费+机械费的5%）
    }
    
    print(f"\n🔍 计算结果验证:")
    calculation_correct = True
    
    for child in child_data:
        resource_code = child['resource_code']
        if resource_code in expected_results:
            expected = expected_results[resource_code]
            actual = child['total_price']
            
            if abs(expected - actual) < 0.01:  # 允许小数点误差
                print(f"✅ {resource_code}: 预期 {expected:.2f}, 实际 {actual:.2f}")
            else:
                print(f"❌ {resource_code}: 预期 {expected:.2f}, 实际 {actual:.2f}")
                calculation_correct = False
    
    return calculation_correct

def test_price_summary():
    """测试价格汇总功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试价格汇总功能")
    print("=" * 60)
    
    calculator = PriceCalculator()
    
    # 创建测试数据
    child_data = [
        {
            "resource_code": "00010701",
            "resource_name": "综合用工三类",
            "category": "人工",
            "unit_price": 120.00,
            "total_price": 96.00
        },
        {
            "resource_code": "99030030",
            "resource_name": "电动打钎机",
            "category": "机械",
            "unit_price": 50.00,
            "total_price": 5.00
        },
        {
            "resource_code": "01010001",
            "resource_name": "水泥",
            "category": "材料",
            "unit_price": 450.00,
            "total_price": 900.00
        }
    ]
    
    summary = calculator.get_price_summary(child_data)
    
    print("📊 价格汇总结果:")
    print(f"- 总项目数: {summary['total_items']}")
    print(f"- 总成本: {summary['total_cost']:.2f} 元")
    print(f"- 有单价项目: {summary['items_with_price']}")
    print(f"- 无单价项目: {summary['items_without_price']}")
    
    print(f"\n📋 分类汇总:")
    for category, cat_data in summary['categories'].items():
        print(f"- {category}: {cat_data['count']} 项, 总成本: {cat_data['total_cost']:.2f} 元")
    
    return len(summary) > 0

def test_price_database_export():
    """测试单价数据库导出功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试单价数据库导出功能")
    print("=" * 60)
    
    calculator = PriceCalculator()
    
    # 导出单价数据库
    export_path = calculator.export_price_database_csv()
    
    if export_path and os.path.exists(export_path):
        print(f"✅ 单价数据库导出成功: {export_path}")
        
        # 检查导出文件
        import pandas as pd
        df = pd.read_csv(export_path, encoding='utf-8-sig')
        
        print(f"📊 导出文件信息:")
        print(f"- 行数: {len(df)}")
        print(f"- 列数: {len(df.columns)}")
        print(f"- 列名: {', '.join(df.columns.tolist())}")
        
        # 显示前几行
        print(f"\n📋 前5行数据:")
        print(df.head().to_string(index=False))
        
        return True
    else:
        print("❌ 单价数据库导出失败")
        return False

def test_price_update():
    """测试单价更新功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试单价更新功能")
    print("=" * 60)
    
    calculator = PriceCalculator()
    
    # 测试更新已存在的资源单价
    resource_code = "00010701"
    old_price = calculator.get_unit_price(resource_code)
    new_price = 150.00
    
    print(f"📝 更新资源 {resource_code}:")
    print(f"- 原单价: {old_price:.2f} 元")
    print(f"- 新单价: {new_price:.2f} 元")
    
    # 更新单价
    calculator.update_unit_price(resource_code, new_price)
    
    # 验证更新
    updated_price = calculator.get_unit_price(resource_code)
    
    if abs(updated_price - new_price) < 0.01:
        print(f"✅ 单价更新成功: {updated_price:.2f} 元")
        
        # 恢复原价格
        calculator.update_unit_price(resource_code, old_price)
        return True
    else:
        print(f"❌ 单价更新失败: {updated_price:.2f} 元")
        return False

if __name__ == "__main__":
    print("🚀 开始测试价格计算功能")
    print("=" * 80)
    
    # 测试单价数据库创建
    database_success = test_price_database_creation()
    
    # 测试单价查询
    lookup_success = test_unit_price_lookup()
    
    # 测试价格计算
    calculation_success = test_price_calculation()
    
    # 测试价格汇总
    summary_success = test_price_summary()
    
    # 测试数据库导出
    export_success = test_price_database_export()
    
    # 测试单价更新
    update_success = test_price_update()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- 数据库创建: {'✅ 正常' if database_success else '❌ 异常'}")
    print(f"- 单价查询: {'✅ 正常' if lookup_success else '❌ 异常'}")
    print(f"- 价格计算: {'✅ 正常' if calculation_success else '❌ 异常'}")
    print(f"- 价格汇总: {'✅ 正常' if summary_success else '❌ 异常'}")
    print(f"- 数据库导出: {'✅ 正常' if export_success else '❌ 异常'}")
    print(f"- 单价更新: {'✅ 正常' if update_success else '❌ 异常'}")
    
    all_success = all([database_success, lookup_success, calculation_success, summary_success, export_success, update_success])
    
    if all_success:
        print("🎉 价格计算功能测试全部通过！")
        print("\n📋 功能特性:")
        print("- ✅ 自动创建单价数据库")
        print("- ✅ 智能单价查询和匹配")
        print("- ✅ 完整价格计算（包括百分比项）")
        print("- ✅ 详细价格汇总统计")
        print("- ✅ 单价数据库导出")
        print("- ✅ 动态单价更新")
        print("\n💡 现在CSV文件将包含正确的单价和合价计算！")
    else:
        print("⚠️ 部分功能需要进一步检查。")
