-- S<PERSON>_SERVER SQL Script
-- Generated on: 2025-06-21 03:46:28
-- Source files: 2 CSV files

-- Table: quota_items
-- Source: quota_items.csv
-- Rows: 5

CREATE TABLE quota_items (
    col_____ NVARCHAR(50) NOT NULL,
    col______1 NVARCHAR(50) NOT NULL,
    col______2 NVARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col______3 NVARCHAR(50) NOT NULL,
    PRIMARY KEY (col_____, col______1)
);

INSERT INTO quota_items (col_____, col______1, col______2, col___, col____, col_____1, col_____2, col______3)
VALUES
    ('D001', '人工挖一般土方 一、二类土', '100m³', 1250.5, 800.0, 150.5, 300.0, '土石方工程'),
    ('D002', '人工挖一般土方 三、四类土', '100m³', 1580.75, 1000.0, 180.75, 400.0, '土石方工程'),
    ('D003', '机械挖一般土方', '100m³', 980.25, 200.0, 380.25, 400.0, '土石方工程'),
    ('D004', '土方回填', '100m³', 850.0, 600.0, 150.0, 100.0, '土石方工程'),
    ('D005', '土方运输', '100m³', 320.5, 150.0, 70.5, 100.0, '土石方工程');

-- Table: resource_items
-- Source: resource_items.csv
-- Rows: 6

CREATE TABLE resource_items (
    col_____ NVARCHAR(50) NOT NULL,
    col______1 NVARCHAR(50) NOT NULL,
    col______2 NVARCHAR(50) NOT NULL,
    col______3 NVARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col______4 NVARCHAR(50),
    col____ NVARCHAR(50) NOT NULL,
    PRIMARY KEY (col_____, col______1)
);

INSERT INTO resource_items (col_____, col______1, col______2, col______3, col___, col______4, col____)
VALUES
    ('R001', '综合工日', '人工', '工日', 120.0, NULL, '本地'),
    ('R002', '普通工', '人工', '工日', 100.0, NULL, '本地'),
    ('R003', '挖掘机', '机械', '台班', 800.0, 'PC200', '租赁公司A'),
    ('R004', '自卸汽车', '机械', '台班', 600.0, '10t', '租赁公司B'),
    ('R005', '水泥', '材料', 't', 450.0, '32.5级', '水泥厂A'),
    ('R006', '砂', '材料', 'm³', 80.0, '中砂', '砂场B');
