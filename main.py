#!/usr/bin/env python3
"""
北京市消耗定额智能提取系统
主程序 - Gradio Web界面
"""

import gradio as gr
import pandas as pd
import os
import asyncio
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import tempfile
import shutil

from src.pdf_processor import PDFProcessor
from src.ai_model_processor import AIModelProcessor
from src.data_processor import DataProcessor
from src.config import Config

class QuotaExtractionApp:
    """定额提取应用主类"""
    
    def __init__(self):
        self.config = Config()
        self.pdf_processor = PDFProcessor()
        self.ai_processor = AIModelProcessor()
        self.data_processor = DataProcessor()
        
    async def process_pdf(
        self,
        pdf_file: str,
        start_page: int,
        end_page: int,
        model_type: str,
        progress: gr.Progress
    ) -> Tuple[str, str, List[Dict]]:
        """
        处理PDF文件并提取定额信息

        Args:
            pdf_file: PDF文件路径
            start_page: 起始页码
            end_page: 结束页码
            model_type: AI模型类型
            progress: Gradio进度条

        Returns:
            Tuple[str, str, List[Dict]]: (CSV文件路径, 处理日志, 处理详情)
        """
        try:
            progress(0, desc="开始处理PDF文件...")
            processing_details = []

            # 1. 提取PDF页面为图片
            progress(0.1, desc="提取PDF页面为图片...")
            images = await self.pdf_processor.extract_pages_as_images(
                pdf_file, start_page, end_page
            )

            total_pages = len(images)
            processed_data = []

            # 2. 逐页处理图片
            for i, image_path in enumerate(images):
                current_page = start_page + i
                progress(
                    0.1 + (i / total_pages) * 0.7,
                    desc=f"使用AI模型处理第 {current_page} 页 ({i+1}/{total_pages})..."
                )

                page_detail = {
                    "page": current_page,
                    "image_path": image_path,
                    "status": "处理中",
                    "model": model_type,
                    "start_time": time.time()
                }

                # 使用AI模型处理图片
                recognition_result = await self.ai_processor.process_image(
                    image_path, model_type
                )

                page_detail["end_time"] = time.time()
                page_detail["duration"] = page_detail["end_time"] - page_detail["start_time"]

                if recognition_result:
                    page_detail["status"] = "识别成功"
                    page_detail["result_length"] = len(recognition_result)

                    # 解析识别结果
                    page_data = self.data_processor.parse_recognition_result(
                        recognition_result, current_page
                    )

                    if page_data:
                        processed_data.extend(page_data)
                        page_detail["extracted_items"] = len(page_data)
                    else:
                        page_detail["extracted_items"] = 0
                        page_detail["status"] = "解析失败"
                else:
                    page_detail["status"] = "识别失败"
                    page_detail["extracted_items"] = 0

                processing_details.append(page_detail)

                # 清理临时图片文件
                try:
                    os.remove(image_path)
                except:
                    pass

            # 3. 生成CSV文件
            progress(0.8, desc="生成CSV文件...")
            csv_path = self.data_processor.generate_csv(processed_data)

            progress(1.0, desc="处理完成!")

            # 生成处理报告
            successful_pages = len([d for d in processing_details if d["status"] == "识别成功"])
            total_items = sum([d.get("extracted_items", 0) for d in processing_details])

            log_message = f"""处理完成！
📊 处理统计:
- 总页数: {total_pages}
- 成功页数: {successful_pages}
- 失败页数: {total_pages - successful_pages}
- 提取定额项: {total_items}
- 使用模型: {self.ai_processor.supported_models.get(model_type, model_type)}
"""

            return csv_path, log_message, processing_details

        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            return None, error_msg, []
    
    def create_interface(self) -> gr.Interface:
        """创建Gradio界面"""
        
        # 创建自定义CSS样式
        custom_css = """
        /* 全局样式 */
        .gradio-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* 主容器样式 */
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 标题样式 */
        .main-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            font-size: 2.8em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: 2px;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        /* 功能卡片样式 */
        .feature-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        /* 按钮样式增强 */
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            border: none !important;
            border-radius: 25px !important;
            color: white !important;
            padding: 12px 30px !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
        }

        .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c) !important;
            border: none !important;
            border-radius: 25px !important;
            color: white !important;
            padding: 10px 25px !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4) !important;
        }

        .btn-secondary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.6) !important;
        }

        /* 文件上传区域美化 */
        .upload-area {
            border: 2px dashed #667eea !important;
            border-radius: 15px !important;
            padding: 40px !important;
            text-align: center !important;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
        }

        .upload-area:hover {
            border-color: #764ba2 !important;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2)) !important;
            transform: scale(1.02) !important;
        }

        /* 状态指示器 */
        .status-success {
            background: linear-gradient(45deg, #4facfe, #00f2fe) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 20px !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3) !important;
        }

        .status-warning {
            background: linear-gradient(45deg, #fa709a, #fee140) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 20px !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3) !important;
        }

        .status-error {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 20px !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
        }

        /* 分隔线美化 */
        .divider {
            height: 3px !important;
            background: linear-gradient(90deg, transparent, #667eea, #764ba2, transparent) !important;
            margin: 40px 0 !important;
            border: none !important;
            border-radius: 2px !important;
        }

        /* 档案管理区域 */
        .archive-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)) !important;
            border-radius: 20px !important;
            padding: 30px !important;
            margin: 30px 0 !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid rgba(102, 126, 234, 0.2) !important;
        }

        /* 数据库转换区域 */
        .database-section {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
            border-radius: 20px !important;
            padding: 30px !important;
            margin: 30px 0 !important;
            border: 1px solid rgba(102, 126, 234, 0.2) !important;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1) !important;
        }

        /* 输入框美化 */
        .gr-textbox, .gr-dropdown, .gr-number {
            border-radius: 10px !important;
            border: 2px solid rgba(102, 126, 234, 0.2) !important;
            transition: all 0.3s ease !important;
        }

        .gr-textbox:focus, .gr-dropdown:focus, .gr-number:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px !important;
                padding: 20px !important;
            }

            .main-title {
                font-size: 2em !important;
            }

            .feature-card {
                padding: 20px !important;
                margin: 10px 0 !important;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 图标样式 */
        .icon {
            font-size: 1.2em;
            margin-right: 8px;
            vertical-align: middle;
        }

        /* 进度条美化 */
        .progress-container {
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 10px !important;
            padding: 3px !important;
            margin: 15px 0 !important;
        }

        .progress-bar {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            border-radius: 8px !important;
            height: 8px !important;
            transition: width 0.3s ease !important;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.5) !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            z-index: 1000 !important;
        }

        .modal-content {
            background: white !important;
            border-radius: 20px !important;
            padding: 30px !important;
            max-width: 600px !important;
            width: 90% !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
            animation: modalSlideIn 0.3s ease-out !important;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        """

        with gr.Blocks(
            title="🏗️ 北京定额表格识别系统 | Beijing Quota Recognition System",
            theme=gr.themes.Soft(),
            css=custom_css
        ) as interface:

            # 主标题区域
            with gr.Row(elem_classes="main-container fade-in-up"):
                with gr.Column():
                    gr.HTML("""
                        <div class="main-title">
                            🏗️ 北京定额表格识别系统
                        </div>
                        <div class="subtitle">
                            <span class="icon">🚀</span>智能PDF识别
                            <span class="icon">📊</span>数据提取
                            <span class="icon">💰</span>价格计算
                            <span class="icon">🗄️</span>数据库转换
                            <span class="icon">📁</span>档案管理
                        </div>
                        <div style="text-align: center; margin: 20px 0; padding: 20px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 1px solid rgba(102, 126, 234, 0.2);">
                            <h3 style="color: #667eea; margin-bottom: 15px;">✨ 系统特色功能</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: left;">
                                <div>🤖 <strong>多模型支持</strong><br><small>24个AI模型可选</small></div>
                                <div>💰 <strong>智能价格计算</strong><br><small>自动单价匹配</small></div>
                                <div>🗄️ <strong>数据库转换</strong><br><small>5种格式支持</small></div>
                                <div>📁 <strong>档案管理</strong><br><small>完整预览合并</small></div>
                            </div>
                        </div>
                        <hr class="divider">
                    """)

            # 主要功能区域
            with gr.Row():
                with gr.Column(scale=2):
                    # AI模型选择卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">🤖</span>AI模型选择
                            </h3>
                        """)

                        with gr.Row():
                            with gr.Column(scale=2):
                                model_dropdown = gr.Dropdown(
                                    label="🎯 选择AI识别模型",
                                    choices=[("请先配置模型", "none")],
                                    value="none",
                                    info="💡 点击配置按钮设置AI模型",
                                    allow_custom_value=True
                                )
                            with gr.Column(scale=1):
                                refresh_models_btn = gr.Button("🔄 刷新", elem_classes="btn-secondary", size="sm")
                            with gr.Column(scale=1):
                                config_models_btn = gr.Button("⚙️ 配置", elem_classes="btn-primary", size="sm")

                    # 文件上传卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📄</span>PDF文件上传
                            </h3>
                        """)
                        pdf_input = gr.File(
                            label="选择PDF定额文件",
                            file_types=[".pdf"],
                            type="filepath",
                            elem_classes="upload-area"
                        )

                    # PDF浏览器卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">🔍</span>PDF智能浏览器
                            </h3>
                        """)

                        # PDF浏览器控制
                        with gr.Row(visible=False) as pdf_controls:
                            with gr.Column(scale=1):
                                current_page = gr.Number(
                                    label="📍 当前页码",
                                    value=1,
                                    minimum=1,
                                    precision=0
                                )
                            with gr.Column(scale=1):
                                total_pages_display = gr.Textbox(
                                    label="📊 总页数",
                                    value="0",
                                    interactive=False
                                )
                            with gr.Column(scale=2):
                                with gr.Row():
                                    prev_page_btn = gr.Button("⬅️ 上一页", size="sm", elem_classes="btn-secondary")
                                    next_page_btn = gr.Button("➡️ 下一页", size="sm", elem_classes="btn-secondary")
                                    zoom_btn = gr.Button("🔍 放大查看", size="sm", elem_classes="btn-secondary")

                        # PDF页面显示
                        pdf_viewer = gr.HTML(
                            label="PDF预览",
                            value="""
                                <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                    <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                                    <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                                    <p style="color: #666;">上传PDF文件后将显示交互式浏览器</p>
                                    <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看、快速跳转</p>
                                </div>
                            """,
                            visible=True
                        )



                    # 页码设置和处理卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📄</span>页码范围设置
                            </h3>
                        """)

                        # 页码范围设置
                        with gr.Row():
                            with gr.Column(scale=1):
                                start_page = gr.Number(
                                    label="📍 起始页码",
                                    value=1,
                                    minimum=1,
                                    precision=0
                                )
                            with gr.Column(scale=1):
                                end_page = gr.Number(
                                    label="📍 结束页码",
                                    value=5,
                                    minimum=1,
                                    precision=0
                                )

                        # 页码设置提示
                        gr.HTML("""
                            <div style="margin-top: 10px; padding: 12px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px; border-left: 4px solid #667eea;">
                                <p style="margin: 0; color: #667eea; font-size: 0.9em;">
                                    <span style="font-size: 1.1em;">💡</span> <strong>建议先测试少量页面，确认效果后再处理全部</strong>
                                </p>
                            </div>
                        """)

                        # 处理按钮
                        gr.HTML("<div style='margin: 20px 0;'></div>")
                        process_btn = gr.Button(
                            "🚀 开始智能提取",
                            variant="primary",
                            size="lg",
                            elem_classes="btn-primary pulse"
                        )

                with gr.Column(scale=1):
                    # 状态监控卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📊</span>实时状态监控
                            </h3>
                        """)

                        # 处理状态
                        status_text = gr.Textbox(
                            label="🔄 处理状态",
                            lines=8,
                            interactive=False,
                            placeholder="等待开始处理..."
                        )

                        # 模型状态显示
                        model_status = gr.Textbox(
                            label="🤖 模型状态",
                            lines=4,
                            interactive=False,
                            value="💡 点击刷新按钮检查模型状态"
                        )

            # 结果输出区域
            with gr.Row():
                with gr.Column():
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📥</span>结果下载
                            </h3>
                        """)
                        csv_output = gr.File(
                            label="💾 下载CSV文件",
                            visible=False
                        )

            # 处理详情展示
            with gr.Row():
                with gr.Column():
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📈</span>处理详情分析
                            </h3>
                        """)
                        processing_details = gr.Dataframe(
                            label="📊 每页处理详情",
                            headers=["页码", "状态", "模型", "耗时(秒)", "提取项数", "识别结果长度"],
                            datatype=["number", "str", "str", "number", "number", "number"],
                            visible=False
                        )

                        # 提取结果预览
                        extraction_preview = gr.Dataframe(
                            label="🔍 提取结果预览",
                            headers=["类型", "编号", "名称", "资源编号", "类别", "消耗量", "单位", "单价", "合价"],
                            datatype=["str", "str", "str", "str", "str", "number", "str", "number", "number"],
                            visible=False
                        )

            # 档案管理区域
            gr.HTML("<hr class='divider'>")

            with gr.Group(elem_classes="archive-section fade-in-up"):
                gr.HTML("""
                    <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">
                        <span class="icon">📁</span>智能档案管理系统
                    </h2>
                    <p style="text-align: center; color: #666; margin-bottom: 30px;">
                        完整的文件预览、批量管理、智能合并功能
                    </p>
                """)

                with gr.Row():
                    with gr.Column(scale=2):
                        # 文件管理操作卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🛠️</span>文件管理操作
                                </h3>
                            """)

                            # 文件列表刷新按钮
                            with gr.Row():
                                refresh_files_btn = gr.Button("🔄 刷新文件列表", elem_classes="btn-secondary")
                                delete_selected_btn = gr.Button("🗑️ 删除选中文件", elem_classes="btn-secondary")
                                merge_selected_btn = gr.Button("🔗 合并选中文件", elem_classes="btn-primary")

                            # 文件列表
                            files_list = gr.CheckboxGroup(
                                label="📋 输出文件列表",
                                choices=[],
                                value=[],
                                interactive=True,
                                info="💡 选择文件进行预览、删除或合并操作"
                            )

                    with gr.Column(scale=1):
                        # 文件预览卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">👀</span>文件预览
                                </h3>
                            """)
                            file_preview = gr.HTML(
                                label="📄 文件内容预览",
                                value="""
                                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                        <div style="font-size: 2.5em; margin-bottom: 15px;">👀</div>
                                        <h4 style="color: #667eea; margin-bottom: 10px;">智能文件预览</h4>
                                        <p style="color: #666;">选择文件查看完整内容预览</p>
                                        <p style="color: #999; font-size: 0.9em;">支持CSV文件完整内容显示</p>
                                    </div>
                                """
                            )

                        # 合并配置卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔗</span>智能文件合并
                                </h3>
                            """)
                            merge_filename = gr.Textbox(
                                label="📝 合并后文件名",
                                placeholder="merged_quota_data.csv",
                                value="merged_quota_data.csv",
                                info="💡 自动添加.csv扩展名"
                            )
                            merge_mode = gr.Radio(
                                label="🎯 合并模式",
                                choices=[
                                    ("📄 按页码排序合并", "by_page"),
                                    ("📝 按文件名排序合并", "by_filename"),
                                    ("⏰ 按创建时间排序合并", "by_time")
                                ],
                                value="by_page",
                                info="💡 推荐使用页码排序保持逻辑顺序"
                            )



            # MCP数据库转换工具
            gr.HTML("<hr class='divider'>")

            with gr.Group(elem_classes="database-section fade-in-up"):
                gr.HTML("""
                    <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">
                        <span class="icon">🗄️</span>MCP数据库转换工具
                    </h2>
                    <p style="text-align: center; color: #666; margin-bottom: 30px;">
                        将CSV文件转换为多种数据库格式，支持SQLite、MySQL、PostgreSQL等
                    </p>
                """)

                with gr.Row():
                    with gr.Column(scale=2):
                        # 数据库转换配置卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔧</span>转换配置
                                </h3>
                            """)

                            db_format = gr.Radio(
                                label="🗄️ 数据库格式",
                                choices=[
                                    ("📱 SQLite数据库文件", "sqlite"),
                                    ("🐬 MySQL SQL脚本", "mysql"),
                                    ("🐘 PostgreSQL SQL脚本", "postgresql"),
                                    ("🏢 SQL Server SQL脚本", "sql_server"),
                                    ("🏛️ Oracle SQL脚本", "oracle")
                                ],
                                value="sqlite",
                                info="💡 SQLite适合本地使用，SQL脚本适合服务器部署"
                            )

                            db_filename = gr.Textbox(
                                label="📝 输出文件名",
                                placeholder="quota_database.db",
                                value="quota_database.db",
                                info="💡 系统会自动添加正确的文件扩展名"
                            )

                            with gr.Row():
                                convert_selected_btn = gr.Button("🔄 转换选中文件", elem_classes="btn-primary")
                                preview_db_btn = gr.Button("👀 预览数据库", elem_classes="btn-secondary")

                    with gr.Column(scale=1):
                        # 转换状态卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">📊</span>转换状态
                                </h3>
                            """)
                            db_conversion_status = gr.Textbox(
                                label="🔄 转换状态",
                                interactive=False,
                                visible=False,
                                placeholder="等待开始转换..."
                            )

                        # 数据库预览卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔍</span>数据库预览
                                </h3>
                            """)
                            db_preview = gr.HTML(
                                label="🗄️ 数据库内容预览",
                                value="""
                                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                        <div style="font-size: 2.5em; margin-bottom: 15px;">🗄️</div>
                                        <h4 style="color: #667eea; margin-bottom: 10px;">数据库预览</h4>
                                        <p style="color: #666;">转换数据库后查看完整预览</p>
                                        <p style="color: #999; font-size: 0.9em;">支持表结构和数据内容预览</p>
                                    </div>
                                """
                            )

            # 输出文件下载区域
            with gr.Row():
                with gr.Column():
                    # 合并文件输出
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📥</span>合并文件下载
                            </h3>
                        """)
                        merged_file_output = gr.File(
                            label="💾 下载合并后的文件",
                            visible=False
                        )

                with gr.Column():
                    # 数据库文件输出
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">🗄️</span>数据库文件下载
                            </h3>
                        """)
                        db_file_output = gr.File(
                            label="💾 下载数据库文件",
                            visible=False
                        )

            # 隐藏状态字段
            archive_status = gr.Textbox(
                label="档案管理状态",
                interactive=False,
                visible=False
            )

            # 页面底部信息
            gr.HTML("""
                <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 20px; border: 1px solid rgba(102, 126, 234, 0.2);">
                    <h3 style="color: #667eea; margin-bottom: 20px;">🎉 系统功能完整</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; text-align: left; margin-bottom: 20px;">
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🤖 智能识别</h4>
                            <p style="color: #666; font-size: 0.9em;">24个AI模型可选<br>专业OCR表格识别<br>高精度数据提取</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">💰 价格计算</h4>
                            <p style="color: #666; font-size: 0.9em;">自动单价匹配<br>智能合价计算<br>百分比项处理</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">📁 档案管理</h4>
                            <p style="color: #666; font-size: 0.9em;">完整文件预览<br>批量管理操作<br>智能文件合并</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🗄️ 数据库转换</h4>
                            <p style="color: #666; font-size: 0.9em;">5种数据库格式<br>智能结构分析<br>完整预览功能</p>
                        </div>
                    </div>
                    <p style="color: #999; font-size: 0.9em; margin-bottom: 0;">
                        <span class="icon">🏗️</span>北京定额表格识别系统 |
                        <span class="icon">🚀</span>专业级AI识别 |
                        <span class="icon">💎</span>企业级功能
                    </p>
                </div>
            """)

            # 模型配置弹出对话框
            with gr.Row(visible=False) as config_modal:
                with gr.Column(elem_classes="modal-overlay"):
                    with gr.Group(elem_classes="modal-content"):
                        gr.HTML("""
                            <div style="text-align: center; margin-bottom: 20px;">
                                <h2 style="color: #667eea; margin-bottom: 10px;">⚙️ AI模型配置</h2>
                                <p style="color: #666;">配置您的AI模型以开始使用智能识别功能</p>
                            </div>
                        """)

                        # Provider选择
                        provider_select = gr.Dropdown(
                            label="🎯 AI服务提供商",
                            choices=[
                                ("阿里云百炼 (通义千问)", "dashscope"),
                                ("DeepSeek", "deepseek"),
                                ("OpenAI", "openai"),
                                ("Anthropic", "anthropic"),
                                ("自定义OpenAI兼容", "custom_openai"),
                                ("本地Ollama", "ollama"),
                                ("LM Studio", "lm_studio")
                            ],
                            value=None,
                            info="选择您要配置的AI服务提供商"
                        )

                        # Provider下的模型选择
                        provider_models = gr.Dropdown(
                            label="🤖 可用模型",
                            choices=[],
                            value=None,
                            info="该服务提供商下的可用模型",
                            visible=True
                        )

                        # 阿里云百炼配置
                        with gr.Group(visible=False) as dashscope_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🎯 阿里云百炼配置</h4>")
                            dashscope_api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="请输入阿里云百炼API密钥",
                                type="password"
                            )
                            dashscope_model = gr.Dropdown(
                                label="模型选择",
                                choices=[
                                    ("通义千问-QVQ-Max", "qvq-max"),
                                    ("通义千问-QVQ-Plus", "qvq-plus")
                                ],
                                value="qvq-max"
                            )

                        # DeepSeek配置
                        with gr.Group(visible=False) as deepseek_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🔬 DeepSeek配置</h4>")
                            deepseek_api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="请输入DeepSeek API密钥",
                                type="password"
                            )

                        # OpenAI配置
                        with gr.Group(visible=False) as openai_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🤖 OpenAI配置</h4>")
                            openai_api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="请输入OpenAI API密钥",
                                type="password"
                            )

                        # Anthropic配置
                        with gr.Group(visible=False) as anthropic_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🧠 Anthropic配置</h4>")
                            anthropic_api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="请输入Anthropic API密钥",
                                type="password"
                            )

                        # 自定义OpenAI兼容配置
                        with gr.Group(visible=False) as custom_openai_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🔧 自定义OpenAI兼容配置</h4>")
                            custom_api_url = gr.Textbox(
                                label="API端点URL",
                                placeholder="例如: https://api.openai.com/v1"
                            )
                            custom_model_name = gr.Textbox(
                                label="模型名称",
                                placeholder="例如: gpt-4-vision-preview"
                            )
                            custom_api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="请输入API密钥",
                                type="password"
                            )

                        # Ollama配置
                        with gr.Group(visible=False) as ollama_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🏠 Ollama配置</h4>")
                            ollama_url = gr.Textbox(
                                label="Ollama服务地址",
                                placeholder="http://localhost:11434",
                                value="http://localhost:11434"
                            )

                        # LM Studio配置
                        with gr.Group(visible=False) as lm_studio_config:
                            gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>🎬 LM Studio配置</h4>")
                            lm_studio_url = gr.Textbox(
                                label="LM Studio服务地址",
                                placeholder="http://127.0.0.1:1234",
                                value="http://127.0.0.1:1234"
                            )

                        # 操作按钮
                        with gr.Row():
                            test_connection_btn = gr.Button("🔄 测试连接", elem_classes="btn-secondary")
                            refresh_models_modal_btn = gr.Button("🔄 刷新模型", elem_classes="btn-secondary")
                            save_config_btn = gr.Button("💾 保存配置", elem_classes="btn-primary")
                            close_modal_btn = gr.Button("❌ 关闭", elem_classes="btn-secondary")

                        # 配置状态显示
                        config_status = gr.HTML(
                            value="""
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px;">
                                    <p style="margin: 0; color: #667eea; text-align: center;">
                                        <span style="font-size: 1.2em;">ℹ️</span> 请选择AI服务提供商开始配置
                                    </p>
                                </div>
                            """
                        )

            # PDF浏览器函数
            def load_pdf(pdf_file):
                """加载PDF文件并初始化浏览器"""
                if not pdf_file:
                    return (
                        "<p style='text-align: center; color: #666;'>📄 上传PDF文件后将显示浏览器</p>",
                        gr.update(visible=False),
                        gr.update(value=1),
                        gr.update(value="0")
                    )

                try:
                    import fitz  # PyMuPDF

                    # 打开PDF文件获取总页数
                    doc = fitz.open(pdf_file)
                    total_pages = len(doc)
                    doc.close()

                    # 显示第一页
                    page_html = render_pdf_page(pdf_file, 1)

                    return (
                        page_html,
                        gr.update(visible=True),
                        gr.update(value=1, maximum=total_pages),
                        gr.update(value=str(total_pages))
                    )

                except Exception as e:
                    error_html = f"""
                    <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                        <h3 style="margin-top: 0; color: #c62828;">❌ PDF加载失败</h3>
                        <p style="color: #666;">错误信息: {str(e)}</p>
                        <p style="color: #666;">请确保上传的是有效的PDF文件</p>
                    </div>
                    """
                    return (
                        error_html,
                        gr.update(visible=False),
                        gr.update(value=1),
                        gr.update(value="0")
                    )

            def render_pdf_page(pdf_file, page_num, zoom_level=2.0):
                """渲染指定页面"""
                try:
                    import fitz  # PyMuPDF
                    import base64

                    # 打开PDF文件
                    doc = fitz.open(pdf_file)
                    total_pages = len(doc)

                    # 确保页码在有效范围内
                    page_num = max(1, min(page_num, total_pages))
                    page = doc[page_num - 1]  # fitz使用0基索引

                    # 渲染页面为图片
                    mat = fitz.Matrix(zoom_level, zoom_level)  # 缩放因子
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("png")

                    # 转换为base64
                    img_base64 = base64.b64encode(img_data).decode()

                    # 生成HTML
                    page_html = f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9; text-align: center;">
                        <h3 style="margin-top: 0; color: #333;">📄 第 {page_num} 页 / 共 {total_pages} 页</h3>
                        <div style="margin: 16px 0; overflow: auto; max-height: 600px;">
                            <img src="data:image/png;base64,{img_base64}"
                                 style="max-width: 100%; border: 1px solid #ccc; border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />
                        </div>
                        <p style="margin-bottom: 0; color: #666; font-size: 12px;">
                            💡 使用上方的导航按钮浏览其他页面，或直接输入页码跳转
                        </p>
                    </div>
                    """

                    doc.close()
                    return page_html

                except Exception as e:
                    return f"""
                    <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                        <h3 style="margin-top: 0; color: #c62828;">❌ 页面渲染失败</h3>
                        <p style="color: #666;">错误信息: {str(e)}</p>
                    </div>
                    """

            def show_page(pdf_file, page_num):
                """显示指定页面"""
                if not pdf_file:
                    return "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

                return render_pdf_page(pdf_file, int(page_num))

            def prev_page(pdf_file, current_page):
                """上一页"""
                if not pdf_file:
                    return current_page, "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

                new_page = max(1, current_page - 1)
                page_html = render_pdf_page(pdf_file, new_page)
                return new_page, page_html

            def next_page(pdf_file, current_page, total_pages):
                """下一页"""
                if not pdf_file:
                    return current_page, "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

                max_pages = int(total_pages) if total_pages.isdigit() else 1
                new_page = min(max_pages, current_page + 1)
                page_html = render_pdf_page(pdf_file, new_page)
                return new_page, page_html

            def zoom_page(pdf_file, current_page):
                """放大查看当前页面"""
                if not pdf_file:
                    return "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

                # 使用更高的缩放级别
                return render_pdf_page(pdf_file, current_page, zoom_level=3.0)

            # 处理函数
            def process_wrapper(pdf_file, model_type, start_page, end_page, progress=gr.Progress()):
                if not pdf_file:
                    return None, "请先上传PDF文件", None, None, gr.update(visible=False)

                if not model_type or model_type == "none":
                    return None, "请选择AI模型", None, None, gr.update(visible=False)

                if start_page > end_page:
                    return None, "起始页码不能大于结束页码", None, None, gr.update(visible=False)

                # 解析模型类型 - 从下拉菜单的值中提取模型键值
                print(f"调试：接收到的model_type: {model_type}")

                # 如果model_type是显示名称，需要转换为模型键值
                actual_model_type = model_type
                if isinstance(model_type, str):
                    # 获取当前可用模型列表
                    available_models = self.ai_processor.get_available_models()

                    # 如果传入的是显示名称，查找对应的键值
                    for key, display_name in available_models.items():
                        if display_name == model_type:
                            actual_model_type = key
                            break

                print(f"调试：实际使用的model_type: {actual_model_type}")

                # 运行异步处理
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    csv_path, log_message, details = loop.run_until_complete(
                        self.process_pdf(pdf_file, int(start_page), int(end_page), actual_model_type, progress)
                    )

                    # 格式化处理详情
                    details_update = gr.update(visible=False)
                    preview_update = gr.update(visible=False)

                    if details:
                        details_data = [
                            [
                                d["page"],
                                d["status"],
                                self.ai_processor.supported_models.get(d["model"], d["model"]),
                                round(d.get("duration", 0), 2),
                                d.get("extracted_items", 0),
                                d.get("result_length", 0)
                            ]
                            for d in details
                        ]
                        details_update = gr.update(value=details_data, visible=True)

                        # 生成预览数据 - 显示更多行以查看完整结果
                        if csv_path:
                            try:
                                import pandas as pd
                                df = pd.read_csv(csv_path, encoding='utf-8-sig')
                                # 显示所有数据，最多100行
                                max_preview_rows = min(len(df), 100)
                                preview_data = df.head(max_preview_rows).values.tolist()
                                preview_update = gr.update(value=preview_data, visible=True)
                                print(f"预览数据：显示 {max_preview_rows}/{len(df)} 行")
                            except Exception as e:
                                print(f"生成预览失败: {e}")

                    return csv_path, log_message, details_update, preview_update, gr.update(visible=bool(csv_path))
                finally:
                    loop.close()

            # 档案管理函数
            def refresh_files_list():
                """刷新输出文件列表"""
                try:
                    import os
                    import glob
                    from datetime import datetime

                    output_dir = "output"
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                        return gr.update(choices=[]), "📁 输出目录为空"

                    # 获取所有CSV文件
                    csv_files = glob.glob(os.path.join(output_dir, "*.csv"))

                    if not csv_files:
                        return gr.update(choices=[]), "📁 没有找到CSV文件"

                    # 获取文件信息
                    file_info = []
                    for file_path in csv_files:
                        filename = os.path.basename(file_path)
                        file_size = os.path.getsize(file_path)
                        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                        # 格式化显示
                        size_mb = file_size / 1024 / 1024
                        time_str = mod_time.strftime("%Y-%m-%d %H:%M")
                        display_name = f"{filename} ({size_mb:.2f}MB, {time_str})"

                        file_info.append((display_name, filename))

                    # 按修改时间排序（最新的在前）
                    file_info.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x[1])), reverse=True)

                    choices = [display_name for display_name, _ in file_info]
                    status_msg = f"📁 找到 {len(choices)} 个CSV文件"

                    return gr.update(choices=choices, value=[]), status_msg

                except Exception as e:
                    return gr.update(choices=[]), f"❌ 刷新文件列表失败: {str(e)}"

            def preview_selected_file(selected_files):
                """预览选中的文件"""
                if not selected_files:
                    return "<p style='text-align: center; color: #666;'>选择文件查看预览</p>"

                try:
                    import pandas as pd
                    import os

                    # 获取第一个选中文件的实际文件名
                    selected_display = selected_files[0]
                    filename = selected_display.split(" (")[0]  # 提取文件名
                    file_path = os.path.join("output", filename)

                    if not os.path.exists(file_path):
                        return f"<p style='color: red;'>❌ 文件不存在: {filename}</p>"

                    # 读取CSV文件
                    df = pd.read_csv(file_path, encoding='utf-8-sig')

                    # 生成完整预览HTML
                    preview_html = f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                        <h3 style="margin-top: 0; color: #333;">📄 {filename}</h3>
                        <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                            <p style="margin: 4px 0;"><strong>📊 文件统计:</strong></p>
                            <p style="margin: 4px 0;">• 总行数: {len(df)} 行</p>
                            <p style="margin: 4px 0;">• 列数: {len(df.columns)} 列</p>
                            <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(file_path) / 1024:.2f} KB</p>
                            <p style="margin: 4px 0;">• 列名: {', '.join(df.columns.tolist())}</p>
                        </div>

                        <h4 style="color: #333; margin-bottom: 12px;">📋 完整文件内容:</h4>
                        <div style="overflow: auto; max-height: 600px; border: 1px solid #ccc; border-radius: 4px; background: white;">
                            {df.to_html(index=True, classes='preview-table', table_id='full-preview-table', escape=False)}
                        </div>

                        <div style="margin-top: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
                            💡 提示: 表格支持滚动查看，显示完整的 {len(df)} 行数据
                        </div>

                        <style>
                        #full-preview-table {{
                            font-size: 11px;
                            border-collapse: collapse;
                            width: 100%;
                            margin: 0;
                        }}
                        #full-preview-table th, #full-preview-table td {{
                            border: 1px solid #ddd;
                            padding: 6px 8px;
                            text-align: left;
                            vertical-align: top;
                        }}
                        #full-preview-table th {{
                            background-color: #f8f9fa;
                            font-weight: bold;
                            position: sticky;
                            top: 0;
                            z-index: 10;
                        }}
                        #full-preview-table tbody tr:nth-child(even) {{
                            background-color: #f9f9f9;
                        }}
                        #full-preview-table tbody tr:hover {{
                            background-color: #e8f4fd;
                        }}
                        #full-preview-table td {{
                            max-width: 200px;
                            word-wrap: break-word;
                            white-space: normal;
                        }}
                        </style>
                    </div>
                    """

                    return preview_html

                except Exception as e:
                    return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"

            def delete_selected_files(selected_files):
                """删除选中的文件"""
                if not selected_files:
                    return gr.update(), "⚠️ 请先选择要删除的文件"

                try:
                    import os

                    deleted_count = 0
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)

                        if os.path.exists(file_path):
                            os.remove(file_path)
                            deleted_count += 1

                    # 刷新文件列表
                    updated_choices, status_msg = refresh_files_list()

                    return updated_choices, f"✅ 成功删除 {deleted_count} 个文件"

                except Exception as e:
                    return gr.update(), f"❌ 删除失败: {str(e)}"

            def merge_selected_files(selected_files, merge_filename, merge_mode):
                """合并选中的文件"""
                if not selected_files:
                    return None, "⚠️ 请先选择要合并的文件", gr.update(visible=False)

                if len(selected_files) < 2:
                    return None, "⚠️ 至少需要选择2个文件进行合并", gr.update(visible=False)

                try:
                    from src.mcp_file_merger import MCPFileMerger
                    import os

                    # 获取实际文件路径
                    file_paths = []
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)
                        if os.path.exists(file_path):
                            file_paths.append(file_path)

                    if not file_paths:
                        return None, "❌ 没有找到有效的文件", gr.update(visible=False)

                    # 确保输出文件名以.csv结尾
                    if not merge_filename.endswith('.csv'):
                        merge_filename += '.csv'

                    output_path = os.path.join("output", merge_filename)

                    # 使用MCP工具合并文件
                    merger = MCPFileMerger()
                    success, message, stats = merger.merge_csv_files(file_paths, output_path, merge_mode)

                    if success:
                        # 生成详细状态信息
                        status_detail = f"""✅ 文件合并成功！

📊 合并统计:
- 合并文件数: {stats['total_files']} 个
- 总数据行数: {stats['total_rows']} 行
- 输出文件大小: {stats['output_size_kb']:.2f} KB
- 合并模式: {stats['merge_mode']}

📁 输出文件: {merge_filename}

📋 源文件详情:"""

                        for file_stat in stats['file_stats']:
                            status_detail += f"\n- {file_stat['filename']}: {file_stat['rows']} 行, {file_stat['size_kb']:.2f} KB"

                        return output_path, status_detail, gr.update(visible=True)
                    else:
                        return None, message, gr.update(visible=False)

                except Exception as e:
                    return None, f"❌ 合并失败: {str(e)}", gr.update(visible=False)

            def update_merge_preview(selected_files, merge_mode):
                """更新合并预览"""
                if not selected_files or len(selected_files) < 2:
                    return "<p style='text-align: center; color: #666;'>选择至少2个文件查看合并预览</p>"

                try:
                    from src.mcp_file_merger import MCPFileMerger
                    import os

                    # 获取实际文件路径
                    file_paths = []
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)
                        if os.path.exists(file_path):
                            file_paths.append(file_path)

                    if not file_paths:
                        return "<p style='color: red;'>❌ 没有找到有效的文件</p>"

                    # 生成合并预览
                    merger = MCPFileMerger()
                    preview_html = merger.generate_merge_preview(file_paths, merge_mode)

                    return preview_html

                except Exception as e:
                    return f"<p style='color: red;'>❌ 生成预览失败: {str(e)}</p>"

            def convert_to_database(selected_files, db_format, db_filename):
                """转换CSV文件为数据库"""
                if not selected_files:
                    return None, "⚠️ 请先选择要转换的CSV文件", gr.update(visible=False), "<p style='color: #666;'>请先转换数据库</p>"

                try:
                    from src.mcp_database_converter import MCPDatabaseConverter
                    import os

                    # 获取实际文件路径
                    file_paths = []
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)
                        if os.path.exists(file_path):
                            file_paths.append(file_path)

                    if not file_paths:
                        return None, "❌ 没有找到有效的CSV文件", gr.update(visible=False), "<p style='color: red;'>❌ 没有找到有效文件</p>"

                    # 确保输出文件名有正确的扩展名
                    if db_format == "sqlite":
                        if not db_filename.endswith('.db') and not db_filename.endswith('.sqlite'):
                            db_filename += '.db'
                    else:
                        if not db_filename.endswith('.sql'):
                            db_filename += '.sql'

                    output_path = os.path.join("output", db_filename)

                    # 使用MCP数据库转换工具
                    converter = MCPDatabaseConverter()

                    if db_format == "sqlite":
                        success, message, stats = converter.convert_to_sqlite(file_paths, output_path)
                    else:
                        success, message, stats = converter.convert_to_sql_script(file_paths, output_path, db_format)

                    if success:
                        # 生成详细状态信息
                        status_detail = f"""✅ 数据库转换成功！

📊 转换统计:
- 源文件数: {stats['total_files']} 个
- 成功转换: {stats['successful_files']} 个
- 总数据行数: {stats['total_rows']} 行
- 输出格式: {db_format.upper()}
- 输出文件: {db_filename}

📋 生成的表:"""

                        for table_info in stats['tables']:
                            status_detail += f"\n- {table_info['name']}: {table_info['rows']} 行 (来源: {table_info['source_file']})"

                        # 生成预览
                        preview_html = converter.preview_database_file(output_path)

                        return output_path, status_detail, gr.update(visible=True), preview_html
                    else:
                        return None, message, gr.update(visible=False), f"<p style='color: red;'>{message}</p>"

                except Exception as e:
                    error_msg = f"❌ 数据库转换失败: {str(e)}"
                    return None, error_msg, gr.update(visible=False), f"<p style='color: red;'>{error_msg}</p>"

            def preview_database_file(selected_files, db_format, db_filename):
                """预览已生成的数据库文件"""
                try:
                    import os

                    # 确保输出文件名有正确的扩展名
                    if db_format == "sqlite":
                        if not db_filename.endswith('.db') and not db_filename.endswith('.sqlite'):
                            db_filename += '.db'
                    else:
                        if not db_filename.endswith('.sql'):
                            db_filename += '.sql'

                    output_path = os.path.join("output", db_filename)

                    if not os.path.exists(output_path):
                        return "<p style='color: orange;'>⚠️ 数据库文件不存在，请先转换CSV文件</p>"

                    from src.mcp_database_converter import MCPDatabaseConverter
                    converter = MCPDatabaseConverter()

                    preview_html = converter.preview_database_file(output_path)
                    return preview_html

                except Exception as e:
                    return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"

            # 刷新模型列表函数
            def refresh_models():
                """刷新可用模型列表"""
                # 重新加载API密钥并获取可用模型
                self.ai_processor.reload_api_keys()
                available_models = self.ai_processor.get_available_models()

                if not available_models:
                    available_models = {"none": "无可用模型 (请配置API密钥)"}

                choices = list(available_models.items())
                value = list(available_models.keys())[0] if available_models else None

                # 更新模型状态文本
                status_text_update = self._get_model_status_text()

                return gr.update(choices=choices, value=value), status_text_update

            # 模态框控制函数
            def show_config_modal():
                """显示配置模态框"""
                return gr.update(visible=True)

            def hide_config_modal():
                """隐藏配置模态框"""
                return gr.update(visible=False)

            # Provider选择处理函数
            def on_provider_change(provider):
                """当选择provider时显示对应的配置区域"""
                if not provider:
                    return (
                        gr.update(visible=False),  # dashscope_config
                        gr.update(visible=False),  # deepseek_config
                        gr.update(visible=False),  # openai_config
                        gr.update(visible=False),  # anthropic_config
                        gr.update(visible=False),  # custom_openai_config
                        gr.update(visible=False),  # ollama_config
                        gr.update(visible=False),  # lm_studio_config
                        gr.update(choices=[], value=None),  # provider_models
                        """
                            <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px;">
                                <p style="margin: 0; color: #667eea; text-align: center;">
                                    <span style="font-size: 1.2em;">ℹ️</span> 请选择AI服务提供商开始配置
                                </p>
                            </div>
                        """
                    )

                # 根据provider显示对应的配置区域
                updates = {
                    "dashscope": (True, False, False, False, False, False, False),
                    "deepseek": (False, True, False, False, False, False, False),
                    "openai": (False, False, True, False, False, False, False),
                    "anthropic": (False, False, False, True, False, False, False),
                    "custom_openai": (False, False, False, False, True, False, False),
                    "ollama": (False, False, False, False, False, True, False),
                    "lm_studio": (False, False, False, False, False, False, True)
                }

                visibility = updates.get(provider, (False, False, False, False, False, False, False))

                provider_names = {
                    "dashscope": "阿里云百炼",
                    "deepseek": "DeepSeek",
                    "openai": "OpenAI",
                    "anthropic": "Anthropic",
                    "custom_openai": "自定义OpenAI兼容",
                    "ollama": "本地Ollama",
                    "lm_studio": "LM Studio"
                }

                # 获取该provider下的可用模型
                provider_models = self.ai_processor.get_models_by_provider(provider)
                model_choices = list(provider_models.items()) if provider_models else []

                status_html = f"""
                    <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px;">
                        <p style="margin: 0; color: #667eea; text-align: center;">
                            <span style="font-size: 1.2em;">🎯</span> 已选择: {provider_names.get(provider, provider)}
                        </p>
                        <p style="margin: 5px 0 0 0; color: #666; text-align: center; font-size: 0.9em;">
                            请填写配置信息并测试连接
                        </p>
                        {f'<p style="margin: 5px 0 0 0; color: #28a745; text-align: center; font-size: 0.9em;">🎉 检测到 {len(model_choices)} 个可用模型</p>' if model_choices else '<p style="margin: 5px 0 0 0; color: #ffc107; text-align: center; font-size: 0.9em;">⚠️ 请先配置API密钥</p>'}
                    </div>
                """

                return (
                    gr.update(visible=visibility[0]),  # dashscope_config
                    gr.update(visible=visibility[1]),  # deepseek_config
                    gr.update(visible=visibility[2]),  # openai_config
                    gr.update(visible=visibility[3]),  # anthropic_config
                    gr.update(visible=visibility[4]),  # custom_openai_config
                    gr.update(visible=visibility[5]),  # ollama_config
                    gr.update(visible=visibility[6]),  # lm_studio_config
                    gr.update(choices=model_choices, value=model_choices[0][0] if model_choices else None),  # provider_models
                    status_html
                )



            def on_model_select(selected_model):
                """当选择模型时显示配置选项"""
                if not selected_model:
                    return (
                        gr.update(visible=False),
                        gr.update(visible=False),
                        gr.update(visible=False),
                        gr.update(visible=False),
                        gr.update(visible=False)
                    )

                model_info = models_config[selected_model]
                current_key = current_config.get(model_info["env_key"], "")

                # 检查是否是自定义OpenAI模型
                is_custom_openai = model_info.get("type") == "custom_openai"

                if is_custom_openai:
                    # 加载自定义配置
                    custom_url = os.environ.get("CUSTOM_OPENAI_API_URL", "")
                    custom_model = os.environ.get("CUSTOM_OPENAI_MODEL_NAME", "")

                    return (
                        gr.update(visible=True, value=current_key),
                        gr.update(visible=True, value="<p>配置自定义OpenAI兼容API端点</p>"),
                        gr.update(visible=True),
                        gr.update(visible=False),
                        gr.update(visible=True)
                    )
                else:
                    return (
                        gr.update(visible=True, value=current_key),
                        gr.update(visible=True, value=f'<p>获取API密钥: <a href="{model_info["url"]}" target="_blank">{model_info["url"]}</a></p>'),
                        gr.update(visible=True),
                        gr.update(visible=False),
                        gr.update(visible=False)
                    )

            def test_model_config(selected_model, api_key):
                """测试模型配置"""
                if not selected_model or not api_key:
                    return gr.update(visible=True, value="❌ 请选择模型并输入API密钥")

                try:
                    model_info = models_config[selected_model]
                    env_key = model_info["env_key"]

                    # 临时设置API密钥
                    old_value = os.environ.get(env_key)
                    os.environ[env_key] = api_key

                    # 测试连接
                    processor = AIModelProcessor()
                    available_models = processor.get_available_models()

                    # 恢复原值
                    if old_value:
                        os.environ[env_key] = old_value
                    elif env_key in os.environ:
                        del os.environ[env_key]

                    if selected_model in available_models:
                        return gr.update(visible=True, value="✅ 连接测试成功！")
                    else:
                        return gr.update(visible=True, value="❌ 连接测试失败，请检查API密钥")

                except Exception as e:
                    return gr.update(visible=True, value=f"❌ 测试失败: {str(e)}")

            def save_model_config(selected_model, api_key, custom_url="", custom_model=""):
                """保存模型配置"""
                if not selected_model or not api_key:
                    return gr.update(visible=True, value="❌ 请选择模型并输入API密钥")

                try:
                    model_info = models_config[selected_model]
                    env_key = model_info["env_key"]

                    # 保存API密钥
                    success = self._save_api_key(env_key, api_key)

                    # 如果是自定义OpenAI模型，还需要保存URL和模型名称
                    if model_info.get("type") == "custom_openai":
                        if not custom_url or not custom_model:
                            return gr.update(visible=True, value="❌ 请填写API端点URL和模型名称")

                        self._save_api_key("CUSTOM_OPENAI_API_URL", custom_url)
                        self._save_api_key("CUSTOM_OPENAI_MODEL_NAME", custom_model)

                    if success:
                        # 更新配置状态显示
                        updated_config = self._load_current_config()
                        status_html = self._generate_config_status_html(updated_config, models_config)

                        return (
                            gr.update(visible=True, value="✅ 配置保存成功！"),
                            gr.update(value=status_html)
                        )
                    else:
                        return (
                            gr.update(visible=True, value="❌ 保存失败"),
                            gr.update()
                        )

                except Exception as e:
                    return (
                        gr.update(visible=True, value=f"❌ 保存失败: {str(e)}"),
                        gr.update()
                    )

            def clear_model_config(selected_model):
                """清除模型配置"""
                if not selected_model:
                    return gr.update(visible=True, value="❌ 请选择模型")

                try:
                    model_info = models_config[selected_model]
                    env_key = model_info["env_key"]

                    # 清除配置
                    success = self._save_api_key(env_key, "")

                    if success:
                        # 更新配置状态显示
                        updated_config = self._load_current_config()
                        status_html = self._generate_config_status_html(updated_config, models_config)

                        return (
                            gr.update(visible=True, value="✅ 配置已清除"),
                            gr.update(value=""),
                            gr.update(value=status_html)
                        )
                    else:
                        return (
                            gr.update(visible=True, value="❌ 清除失败"),
                            gr.update(),
                            gr.update()
                        )

                except Exception as e:
                    return (
                        gr.update(visible=True, value=f"❌ 清除失败: {str(e)}"),
                        gr.update(),
                        gr.update()
                    )

            # 绑定事件
            # PDF上传和浏览器
            pdf_input.change(
                fn=load_pdf,
                inputs=[pdf_input],
                outputs=[pdf_viewer, pdf_controls, current_page, total_pages_display]
            )

            # 页面导航
            current_page.change(
                fn=show_page,
                inputs=[pdf_input, current_page],
                outputs=[pdf_viewer]
            )

            prev_page_btn.click(
                fn=prev_page,
                inputs=[pdf_input, current_page],
                outputs=[current_page, pdf_viewer]
            )

            next_page_btn.click(
                fn=next_page,
                inputs=[pdf_input, current_page, total_pages_display],
                outputs=[current_page, pdf_viewer]
            )

            zoom_btn.click(
                fn=zoom_page,
                inputs=[pdf_input, current_page],
                outputs=[pdf_viewer]
            )

            process_btn.click(
                fn=process_wrapper,
                inputs=[pdf_input, model_dropdown, start_page, end_page],
                outputs=[csv_output, status_text, processing_details, extraction_preview, csv_output],
                show_progress=True
            )

            refresh_models_btn.click(
                fn=refresh_models,
                outputs=[model_dropdown, model_status]
            )

            config_models_btn.click(
                fn=show_config_modal,
                outputs=[config_modal]
            )

            close_modal_btn.click(
                fn=hide_config_modal,
                outputs=[config_modal]
            )

            # 测试连接功能
            def test_api_connection(provider, dashscope_key, dashscope_model, deepseek_key, openai_key, anthropic_key, custom_url, custom_model, custom_key, ollama_url, lm_studio_url):
                """测试API连接"""
                if not provider:
                    return """
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 请先选择AI服务提供商
                            </p>
                        </div>
                    """

                # 根据provider确定测试参数
                test_configs = {
                    "dashscope": {
                        "key": dashscope_key,
                        "model": dashscope_model or "qvq-max",
                        "service_type": "qwen_qvq_max" if (dashscope_model == "qvq-max" or not dashscope_model) else "qwen_qvq_plus",
                        "url": "https://dashscope.aliyuncs.com/compatible-mode/v1"
                    },
                    "deepseek": {
                        "key": deepseek_key,
                        "model": "deepseek-chat",
                        "service_type": "deepseek_api",
                        "url": "https://api.deepseek.com/v1"
                    },
                    "openai": {
                        "key": openai_key,
                        "model": "gpt-4-vision-preview",
                        "service_type": "openai_gpt4v",
                        "url": "https://api.openai.com/v1"
                    },
                    "anthropic": {
                        "key": anthropic_key,
                        "model": "claude-3-sonnet-20240229",
                        "service_type": "claude_vision",
                        "url": "https://api.anthropic.com/v1"
                    },
                    "custom_openai": {
                        "key": custom_key,
                        "model": custom_model,
                        "url": custom_url,
                        "service_type": "custom_openai"
                    }
                }

                if provider not in test_configs:
                    return f"""
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 暂不支持 {provider} 的连接测试
                            </p>
                        </div>
                    """

                config = test_configs[provider]

                # 验证必要参数
                if not config["key"]:
                    return """
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 请输入API密钥
                            </p>
                        </div>
                    """

                if provider == "custom_openai" and (not custom_url or not custom_model):
                    return """
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 请填写完整的自定义模型配置
                            </p>
                        </div>
                    """

                # 显示测试中状态
                testing_html = """
                    <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.1)); border-radius: 10px; border-left: 4px solid #ffc107;">
                        <p style="margin: 0; color: #ffc107; text-align: center;">
                            <span style="font-size: 1.2em;">🔄</span> 正在测试连接，请稍候...
                        </p>
                    </div>
                """

                try:
                    import asyncio
                    import time

                    # 运行测试
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    start_time = time.time()

                    try:
                        result = loop.run_until_complete(
                            self._test_model_connection(
                                config["service_type"],
                                config["key"],
                                config.get("url", ""),
                                config["model"]
                            )
                        )

                        end_time = time.time()
                        duration = round(end_time - start_time, 2)

                        if result["success"]:
                            return f"""
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.1)); border-radius: 10px; border-left: 4px solid #28a745;">
                                    <div style="text-align: center; margin-bottom: 10px;">
                                        <span style="font-size: 1.5em; color: #28a745;">✅</span>
                                        <h4 style="margin: 5px 0; color: #28a745;">连接测试成功！</h4>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.8); padding: 10px; border-radius: 8px; font-size: 0.9em;">
                                        <p style="margin: 3px 0; color: #333;"><strong>🎯 服务:</strong> {result.get('service_name', api_service)}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>⏱️ 响应时间:</strong> {duration}秒</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>🤖 模型:</strong> {result.get('model', 'N/A')}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>📝 响应长度:</strong> {result.get('response_length', 0)} 字符</p>
                                        {f'<p style="margin: 3px 0; color: #666;"><strong>💬 响应预览:</strong> {result.get("response_preview", "")}</p>' if result.get("response_preview") else ''}
                                    </div>
                                </div>
                            """
                        else:
                            return f"""
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                                    <div style="text-align: center; margin-bottom: 10px;">
                                        <span style="font-size: 1.5em; color: #ff6b6b;">❌</span>
                                        <h4 style="margin: 5px 0; color: #ff6b6b;">连接测试失败</h4>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.8); padding: 10px; border-radius: 8px; font-size: 0.9em;">
                                        <p style="margin: 3px 0; color: #333;"><strong>🎯 服务:</strong> {result.get('service_name', api_service)}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>⏱️ 测试时间:</strong> {duration}秒</p>
                                        <p style="margin: 3px 0; color: #ff6b6b;"><strong>❌ 错误信息:</strong> {result.get('error', '未知错误')}</p>
                                        {f'<p style="margin: 3px 0; color: #666;"><strong>💡 建议:</strong> {result.get("suggestion", "")}</p>' if result.get("suggestion") else ''}
                                    </div>
                                </div>
                            """
                    finally:
                        loop.close()

                except Exception as e:
                    return f"""
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 测试过程中发生错误: {str(e)}
                            </p>
                        </div>
                    """

            # Provider选择事件绑定
            provider_select.change(
                fn=on_provider_change,
                inputs=[provider_select],
                outputs=[
                    dashscope_config, deepseek_config, openai_config, anthropic_config,
                    custom_openai_config, ollama_config, lm_studio_config, provider_models, config_status
                ]
            )

            # 模型配置模态框事件绑定
            test_connection_btn.click(
                fn=test_api_connection,
                inputs=[
                    provider_select, dashscope_api_key, dashscope_model, deepseek_api_key,
                    openai_api_key, anthropic_api_key, custom_api_url, custom_model_name,
                    custom_api_key, ollama_url, lm_studio_url
                ],
                outputs=[config_status]
            )

            refresh_models_modal_btn.click(
                fn=refresh_models,
                outputs=[model_dropdown, model_status]
            )

            # 保存配置功能
            def save_api_configuration(provider, dashscope_key, dashscope_model, deepseek_key, openai_key, anthropic_key, custom_url, custom_model, custom_key, ollama_url, lm_studio_url):
                """保存API配置"""
                if not provider:
                    return """
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 请先选择AI服务提供商
                            </p>
                        </div>
                    """

                try:
                    # 根据provider保存配置
                    save_configs = {
                        "dashscope": {
                            "key": dashscope_key,
                            "env_key": "DASHSCOPE_API_KEY",
                            "name": "阿里云百炼"
                        },
                        "deepseek": {
                            "key": deepseek_key,
                            "env_key": "DEEPSEEK_API_KEY",
                            "name": "DeepSeek"
                        },
                        "openai": {
                            "key": openai_key,
                            "env_key": "OPENAI_API_KEY",
                            "name": "OpenAI"
                        },
                        "anthropic": {
                            "key": anthropic_key,
                            "env_key": "ANTHROPIC_API_KEY",
                            "name": "Anthropic"
                        },
                        "custom_openai": {
                            "key": custom_key,
                            "url": custom_url,
                            "model": custom_model,
                            "name": "自定义OpenAI兼容"
                        }
                    }

                    if provider not in save_configs:
                        return f"""
                            <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                                <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                    <span style="font-size: 1.2em;">❌</span> 暂不支持 {provider} 的配置保存
                                </p>
                            </div>
                        """

                    config = save_configs[provider]

                    if provider == "custom_openai":
                        if not custom_key or not custom_url or not custom_model:
                            return """
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                                    <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                        <span style="font-size: 1.2em;">❌</span> 请填写完整的自定义模型配置
                                    </p>
                                </div>
                            """

                        # 保存自定义模型配置
                        success1 = self._save_api_key("CUSTOM_OPENAI_API_KEY", custom_key)
                        success2 = self._save_api_key("CUSTOM_OPENAI_API_URL", custom_url)
                        success3 = self._save_api_key("CUSTOM_OPENAI_MODEL_NAME", custom_model)

                        if success1 and success2 and success3:
                            # 重新加载AI处理器以应用新配置
                            self.ai_processor.reload_api_keys()

                            return f"""
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.1)); border-radius: 10px; border-left: 4px solid #28a745;">
                                    <div style="text-align: center; margin-bottom: 10px;">
                                        <span style="font-size: 1.5em; color: #28a745;">✅</span>
                                        <h4 style="margin: 5px 0; color: #28a745;">配置保存成功！</h4>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.8); padding: 10px; border-radius: 8px; font-size: 0.9em;">
                                        <p style="margin: 3px 0; color: #333;"><strong>🎯 服务:</strong> {config["name"]}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>🤖 模型:</strong> {custom_model}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>🌐 端点:</strong> {custom_url}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>🔑 密钥:</strong> {'*' * (len(custom_key) - 4) + custom_key[-4:] if len(custom_key) > 4 else '****'}</p>
                                        <p style="margin: 8px 0 3px 0; color: #28a745; font-weight: bold;">💡 配置已生效，请点击"🔄 刷新模型"按钮更新主界面模型列表</p>
                                    </div>
                                </div>
                            """
                    else:
                        if not config["key"]:
                            return """
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                                    <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                        <span style="font-size: 1.2em;">❌</span> 请输入API密钥
                                    </p>
                                </div>
                            """

                        # 保存API密钥
                        success = self._save_api_key(config["env_key"], config["key"])

                        if success:
                            # 重新加载AI处理器以应用新配置
                            self.ai_processor.reload_api_keys()

                            return f"""
                                <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.1)); border-radius: 10px; border-left: 4px solid #28a745;">
                                    <div style="text-align: center; margin-bottom: 10px;">
                                        <span style="font-size: 1.5em; color: #28a745;">✅</span>
                                        <h4 style="margin: 5px 0; color: #28a745;">配置保存成功！</h4>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.8); padding: 10px; border-radius: 8px; font-size: 0.9em;">
                                        <p style="margin: 3px 0; color: #333;"><strong>🎯 服务:</strong> {config["name"]}</p>
                                        <p style="margin: 3px 0; color: #333;"><strong>🔑 密钥:</strong> {'*' * (len(config["key"]) - 4) + config["key"][-4:] if len(config["key"]) > 4 else '****'}</p>
                                        <p style="margin: 8px 0 3px 0; color: #28a745; font-weight: bold;">💡 配置已生效，请点击"🔄 刷新模型"按钮更新主界面模型列表</p>
                                    </div>
                                </div>
                            """

                    return """
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 保存失败
                            </p>
                        </div>
                    """

                except Exception as e:
                    return f"""
                        <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.1)); border-radius: 10px; border-left: 4px solid #ff6b6b;">
                            <p style="margin: 0; color: #ff6b6b; text-align: center;">
                                <span style="font-size: 1.2em;">❌</span> 保存过程中发生错误: {str(e)}
                            </p>
                        </div>
                    """

            save_config_btn.click(
                fn=save_api_configuration,
                inputs=[
                    provider_select, dashscope_api_key, dashscope_model, deepseek_api_key,
                    openai_api_key, anthropic_api_key, custom_api_url, custom_model_name,
                    custom_api_key, ollama_url, lm_studio_url
                ],
                outputs=[config_status]
            )

            # 档案管理事件绑定
            refresh_files_btn.click(
                fn=refresh_files_list,
                outputs=[files_list, archive_status]
            )

            files_list.change(
                fn=preview_selected_file,
                inputs=[files_list],
                outputs=[file_preview]
            )

            files_list.change(
                fn=update_merge_preview,
                inputs=[files_list, merge_mode],
                outputs=[file_preview]
            )

            merge_mode.change(
                fn=update_merge_preview,
                inputs=[files_list, merge_mode],
                outputs=[file_preview]
            )

            delete_selected_btn.click(
                fn=delete_selected_files,
                inputs=[files_list],
                outputs=[files_list, archive_status]
            )

            merge_selected_btn.click(
                fn=merge_selected_files,
                inputs=[files_list, merge_filename, merge_mode],
                outputs=[merged_file_output, archive_status, merged_file_output]
            )

            # 数据库转换事件绑定
            convert_selected_btn.click(
                fn=convert_to_database,
                inputs=[files_list, db_format, db_filename],
                outputs=[db_file_output, db_conversion_status, db_file_output, db_preview]
            )

            preview_db_btn.click(
                fn=preview_database_file,
                inputs=[files_list, db_format, db_filename],
                outputs=[db_preview]
            )

            # 当数据库格式改变时，自动更新文件名扩展名
            def update_db_filename(format_type, current_filename):
                """根据数据库格式更新文件名"""
                base_name = current_filename.split('.')[0] if '.' in current_filename else current_filename

                if format_type == "sqlite":
                    return f"{base_name}.db"
                else:
                    return f"{base_name}.sql"

            db_format.change(
                fn=update_db_filename,
                inputs=[db_format, db_filename],
                outputs=[db_filename]
            )

            # 页面加载时自动刷新文件列表
            interface.load(
                fn=refresh_files_list,
                outputs=[files_list, archive_status]
            )

        return interface

    def _load_current_config(self) -> dict:
        """加载当前配置"""
        import os
        from dotenv import load_dotenv

        # 加载.env文件
        load_dotenv()

        config = {}
        env_keys = [
            "OPENAI_API_KEY",
            "ANTHROPIC_API_KEY",
            "GOOGLE_API_KEY",
            "DEEPSEEK_API_KEY",
            "DASHSCOPE_API_KEY"
        ]

        for key in env_keys:
            value = os.environ.get(key, "")
            config[key] = value

        return config

    def _generate_config_status_html(self, current_config: dict, models_config: dict) -> str:
        """生成配置状态HTML"""
        configured_count = len([k for k, v in current_config.items() if v])
        total_count = len(models_config)

        status_html = f"""
        <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3>📊 配置状态</h3>
            <p><strong>已配置模型</strong>: {configured_count} / {total_count}</p>
        """

        for model_key, model_info in models_config.items():
            env_key = model_info["env_key"]
            is_configured = bool(current_config.get(env_key))
            status_icon = "✅" if is_configured else "❌"
            status_html += f"<p>{status_icon} {model_info['name']}</p>"

        status_html += "</div>"
        return status_html

    def _save_api_key(self, env_key: str, api_key: str) -> bool:
        """保存API密钥到.env文件"""
        try:
            import os
            from dotenv import load_dotenv, set_key

            env_file = ".env"

            # 确保.env文件存在
            if not os.path.exists(env_file):
                with open(env_file, 'w') as f:
                    f.write("")

            # 设置环境变量
            set_key(env_file, env_key, api_key)

            # 同时设置到当前环境
            os.environ[env_key] = api_key

            return True
        except Exception as e:
            print(f"保存API密钥失败: {e}")
            return False

    def _get_model_status_text(self) -> str:
        """获取模型状态文本"""
        available_models = self.ai_processor.get_available_models()

        status_lines = []

        if available_models:
            status_lines.append("✅ 可用模型:")
            for model_id, model_name in available_models.items():
                status_lines.append(f"• {model_name}")
        else:
            status_lines.append("❌ 无可用模型")

        status_lines.append("")
        status_lines.append("📋 模型配置状态:")

        # 检查API模型配置
        api_models = {
            "DASHSCOPE_API_KEY": "阿里通义千问-QVQ",
            "DEEPSEEK_API_KEY": "DeepSeek API",
            "OPENAI_API_KEY": "OpenAI GPT-4V",
            "ANTHROPIC_API_KEY": "Claude Vision",
            "GOOGLE_API_KEY": "Google Gemini"
        }

        import os
        for env_key, model_name in api_models.items():
            is_configured = bool(os.environ.get(env_key))
            status_icon = "✅" if is_configured else "❌"
            status_lines.append(f"{status_icon} {model_name}")

        # 检查Ollama状态
        if self.ai_processor._check_ollama_available():
            ollama_models = self.ai_processor.get_ollama_models()
            if ollama_models:
                status_lines.append(f"✅ Ollama ({len(ollama_models)} 个模型)")
            else:
                status_lines.append("⚠️ Ollama运行中但无模型")
        else:
            status_lines.append("❌ Ollama未运行")
            status_lines.append("")
            status_lines.append("💡 安装Ollama:")
            status_lines.append("1. 访问 https://ollama.ai")
            status_lines.append("2. 下载并安装Ollama")
            status_lines.append("3. 运行: ollama pull qwen2.5-vl:7b")

        # 检查LM Studio状态
        if self.ai_processor._check_lm_studio_available():
            lm_studio_models = self.ai_processor.get_lm_studio_models()
            if lm_studio_models:
                status_lines.append(f"✅ LM Studio ({len(lm_studio_models)} 个模型)")
            else:
                status_lines.append("⚠️ LM Studio运行中但无模型")
        else:
            status_lines.append("❌ LM Studio未运行")
            status_lines.append("")
            status_lines.append("💡 启动LM Studio:")
            status_lines.append("1. 启动LM Studio应用")
            status_lines.append("2. 加载模型到服务器")
            status_lines.append("3. 确保服务运行在 http://127.0.0.1:1234")

        return "\n".join(status_lines)

    async def _test_model_connection(self, service_type: str, api_key: str, api_url: str, model_name: str) -> dict:
        """测试模型连接"""
        try:
            import requests
            import json

            # 根据service_type确定实际的API URL和模型名
            if service_type in ["qwen_qvq_max", "qwen_qvq_plus"]:
                # 阿里云百炼 - 使用官方文档的配置
                actual_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
                actual_model = "qvq-max" if service_type == "qwen_qvq_max" else "qvq-plus"
                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
                service_name = "阿里通义千问-QVQ-Max" if service_type == "qwen_qvq_max" else "阿里通义千问-QVQ-Plus"
            elif service_type == "deepseek_api":
                actual_url = "https://api.deepseek.com/v1/chat/completions"
                actual_model = "deepseek-chat"
                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
                service_name = "DeepSeek API"
            elif service_type == "openai_gpt4v":
                actual_url = "https://api.openai.com/v1/chat/completions"
                actual_model = "gpt-4-vision-preview"
                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
                service_name = "OpenAI GPT-4 Vision"
            elif service_type == "claude_vision":
                actual_url = "https://api.anthropic.com/v1/messages"
                actual_model = "claude-3-sonnet-20240229"
                headers = {"x-api-key": api_key, "Content-Type": "application/json", "anthropic-version": "2023-06-01"}
                service_name = "Anthropic Claude Vision"
            elif service_type == "custom_openai":
                actual_url = f"{api_url.rstrip('/')}/chat/completions"
                actual_model = model_name
                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
                service_name = f"自定义模型 ({model_name})"
            else:
                return {
                    "success": False,
                    "error": f"不支持的服务类型: {service_type}",
                    "suggestion": "请选择支持的AI服务"
                }

            # 构建测试请求
            if service_type == "claude_vision":
                # Claude API格式不同
                test_data = {
                    "model": actual_model,
                    "max_tokens": 50,
                    "messages": [
                        {
                            "role": "user",
                            "content": "请简单回复'连接测试成功'"
                        }
                    ]
                }
            else:
                # OpenAI兼容格式 (包括阿里云百炼)
                test_data = {
                    "model": actual_model,
                    "messages": [
                        {
                            "role": "user",
                            "content": "请简单回复'连接测试成功'"
                        }
                    ],
                    "max_tokens": 50,
                    "temperature": 0.1
                }

            # 发送测试请求
            response = requests.post(
                actual_url,
                headers=headers,
                json=test_data,
                timeout=30
            )

            if response.status_code == 200:
                response_data = response.json()

                # 解析响应内容
                if service_type == "claude_vision":
                    content = response_data.get("content", [{}])[0].get("text", "")
                else:
                    content = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                return {
                    "success": True,
                    "service_name": service_name,
                    "model": actual_model,
                    "response_length": len(content),
                    "response_preview": content[:100] + "..." if len(content) > 100 else content
                }
            else:
                error_msg = f"HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        error_msg = error_data["error"].get("message", error_msg)
                except:
                    pass

                # 根据错误码提供建议
                suggestions = {
                    401: "API密钥无效，请检查密钥是否正确",
                    403: "API密钥权限不足或账户余额不足",
                    404: "API端点不存在，请检查URL是否正确",
                    429: "请求频率过高，请稍后重试",
                    500: "服务器内部错误，请稍后重试"
                }

                return {
                    "success": False,
                    "service_name": service_name,
                    "error": error_msg,
                    "suggestion": suggestions.get(response.status_code, "请检查网络连接和配置")
                }

        except requests.exceptions.Timeout:
            return {
                "success": False,
                "service_name": service_name if 'service_name' in locals() else service_type,
                "error": "连接超时",
                "suggestion": "请检查网络连接或稍后重试"
            }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "service_name": service_name if 'service_name' in locals() else service_type,
                "error": "无法连接到服务器",
                "suggestion": "请检查网络连接和API端点URL"
            }
        except Exception as e:
            return {
                "success": False,
                "service_name": service_name if 'service_name' in locals() else service_type,
                "error": str(e),
                "suggestion": "请检查配置信息是否正确"
            }

def main():
    """主函数"""
    app = QuotaExtractionApp()
    interface = app.create_interface()
    
    # 启动界面
    interface.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        debug=True
    )

if __name__ == "__main__":
    main()
