#!/usr/bin/env python3
"""
北京市消耗定额智能提取系统
主程序 - Gradio Web界面
"""

import gradio as gr
import pandas as pd
import os
import asyncio
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import tempfile
import shutil

from src.pdf_processor import PDFProcessor
from src.ai_model_processor import AIModelProcessor
from src.data_processor import DataProcessor
from src.config import Config

class QuotaExtractionApp:
    """定额提取应用主类"""
    
    def __init__(self):
        self.config = Config()
        self.pdf_processor = PDFProcessor()
        self.ai_processor = AIModelProcessor()
        self.data_processor = DataProcessor()
        
    async def process_pdf(
        self,
        pdf_file: str,
        start_page: int,
        end_page: int,
        model_type: str,
        progress: gr.Progress
    ) -> Tuple[str, str, List[Dict]]:
        """
        处理PDF文件并提取定额信息

        Args:
            pdf_file: PDF文件路径
            start_page: 起始页码
            end_page: 结束页码
            model_type: AI模型类型
            progress: Gradio进度条

        Returns:
            Tuple[str, str, List[Dict]]: (CSV文件路径, 处理日志, 处理详情)
        """
        try:
            progress(0, desc="开始处理PDF文件...")
            processing_details = []

            # 1. 提取PDF页面为图片
            progress(0.1, desc="提取PDF页面为图片...")
            images = await self.pdf_processor.extract_pages_as_images(
                pdf_file, start_page, end_page
            )

            total_pages = len(images)
            processed_data = []

            # 2. 逐页处理图片
            for i, image_path in enumerate(images):
                current_page = start_page + i
                progress(
                    0.1 + (i / total_pages) * 0.7,
                    desc=f"使用AI模型处理第 {current_page} 页 ({i+1}/{total_pages})..."
                )

                page_detail = {
                    "page": current_page,
                    "image_path": image_path,
                    "status": "处理中",
                    "model": model_type,
                    "start_time": time.time()
                }

                # 使用AI模型处理图片
                recognition_result = await self.ai_processor.process_image(
                    image_path, model_type
                )

                page_detail["end_time"] = time.time()
                page_detail["duration"] = page_detail["end_time"] - page_detail["start_time"]

                if recognition_result:
                    page_detail["status"] = "识别成功"
                    page_detail["result_length"] = len(recognition_result)

                    # 解析识别结果
                    page_data = self.data_processor.parse_recognition_result(
                        recognition_result, current_page
                    )

                    if page_data:
                        processed_data.extend(page_data)
                        page_detail["extracted_items"] = len(page_data)
                    else:
                        page_detail["extracted_items"] = 0
                        page_detail["status"] = "解析失败"
                else:
                    page_detail["status"] = "识别失败"
                    page_detail["extracted_items"] = 0

                processing_details.append(page_detail)

                # 清理临时图片文件
                try:
                    os.remove(image_path)
                except:
                    pass

            # 3. 生成CSV文件
            progress(0.8, desc="生成CSV文件...")
            csv_path = self.data_processor.generate_csv(processed_data)

            progress(1.0, desc="处理完成!")

            # 生成处理报告
            successful_pages = len([d for d in processing_details if d["status"] == "识别成功"])
            total_items = sum([d.get("extracted_items", 0) for d in processing_details])

            log_message = f"""处理完成！
📊 处理统计:
- 总页数: {total_pages}
- 成功页数: {successful_pages}
- 失败页数: {total_pages - successful_pages}
- 提取定额项: {total_items}
- 使用模型: {self.ai_processor.supported_models.get(model_type, model_type)}
"""

            return csv_path, log_message, processing_details

        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            return None, error_msg, []
    
    def create_interface(self) -> gr.Interface:
        """创建Gradio界面"""
        
        with gr.Blocks(
            title="北京市消耗定额智能提取系统",
            theme=gr.themes.Soft()
        ) as interface:

            gr.Markdown("""
            # 北京市消耗定额智能提取系统 (AI模型版)

            本系统使用多种AI模型API自动从PDF定额文件中提取表格数据，并生成包含价格计算公式的CSV文件。

            ## 使用说明：
            1. 点击"⚙️ 配置模型"设置AI模型API密钥
            2. 上传PDF定额文件
            3. 选择AI识别模型
            4. 设置要提取的页码范围
            5. 点击"开始提取"按钮
            6. 查看处理过程和下载CSV文件
            """)

            with gr.Row():
                with gr.Column(scale=2):
                            # 文件上传
                            pdf_input = gr.File(
                                label="上传PDF定额文件",
                                file_types=[".pdf"],
                                type="filepath"
                            )

                            # PDF预览区域
                            with gr.Row():
                                with gr.Column():
                                    pdf_preview = gr.HTML(
                                        label="PDF预览",
                                        value="<p style='text-align: center; color: #666;'>📄 上传PDF文件后将显示预览</p>",
                                        visible=True
                                    )

                            # AI模型选择
                            available_models = self.ai_processor.get_available_models()
                            if not available_models:
                                available_models = {"none": "无可用模型 (请配置API密钥)"}

                            model_dropdown = gr.Dropdown(
                                label="选择AI识别模型",
                                choices=list(available_models.items()),
                                value=list(available_models.keys())[0] if available_models else None,
                                info="不同模型的识别效果和速度可能不同",
                                allow_custom_value=True
                            )

                            # 模型管理按钮
                            with gr.Row():
                                refresh_models_btn = gr.Button("🔄 刷新模型列表", size="sm", variant="secondary")
                                config_models_btn = gr.Button("⚙️ 配置模型", size="sm", variant="primary")

                            # 页码范围设置
                            with gr.Row():
                                start_page = gr.Number(
                                    label="起始页码",
                                    value=1,
                                    minimum=1,
                                    precision=0
                                )
                                end_page = gr.Number(
                                    label="结束页码",
                                    value=5,
                                    minimum=1,
                                    precision=0,
                                    info="建议先测试少量页面"
                                )

                            # 处理按钮
                            process_btn = gr.Button(
                                "开始提取",
                                variant="primary",
                                size="lg"
                            )

                with gr.Column(scale=1):
                            # 处理状态
                            status_text = gr.Textbox(
                                label="处理状态",
                                lines=8,
                                interactive=False
                            )

                            # 模型状态显示
                            model_status = gr.Textbox(
                                label="模型状态",
                                lines=4,
                                interactive=False,
                                value=self._get_model_status_text()
                            )

            # 结果输出
            with gr.Row():
                csv_output = gr.File(
                    label="下载CSV文件",
                    visible=False
                )

            # 处理详情展示
            with gr.Row():
                with gr.Column():
                    processing_details = gr.Dataframe(
                        label="每页处理详情",
                        headers=["页码", "状态", "模型", "耗时(秒)", "提取项数", "识别结果长度"],
                        datatype=["number", "str", "str", "number", "number", "number"],
                        visible=False
                    )

                    # 提取结果预览
                    extraction_preview = gr.Dataframe(
                        label="提取结果预览",
                        headers=["类型", "编号", "名称", "资源编号", "类别", "消耗量", "单位"],
                        datatype=["str", "str", "str", "str", "str", "number", "str"],
                        visible=False
                    )

            # 模型配置对话框
            with gr.Row(visible=False) as config_dialog:
                with gr.Column():
                    gr.Markdown("## ⚙️ AI模型配置")

                    # 预定义的模型配置
                    models_config = {
                        "qwen_qvq_max": {
                            "name": "阿里通义千问-QVQ-Max",
                            "env_key": "DASHSCOPE_API_KEY",
                            "description": "阿里云百炼平台的QVQ视觉推理模型，具有强大的思维链推理能力",
                            "url": "https://bailian.console.aliyun.com/",
                            "icon": "🎯"
                        },
                        "deepseek_api": {
                            "name": "DeepSeek API",
                            "env_key": "DEEPSEEK_API_KEY",
                            "description": "DeepSeek的视觉理解模型，性价比高",
                            "url": "https://platform.deepseek.com/api_keys",
                            "icon": "🔬"
                        },
                        "openai_gpt4v": {
                            "name": "OpenAI GPT-4 Vision",
                            "env_key": "OPENAI_API_KEY",
                            "description": "OpenAI的GPT-4视觉模型，支持图像理解",
                            "url": "https://platform.openai.com/api-keys",
                            "icon": "🤖"
                        },
                        "claude_vision": {
                            "name": "Anthropic Claude Vision",
                            "env_key": "ANTHROPIC_API_KEY",
                            "description": "Anthropic的Claude-3视觉模型",
                            "url": "https://console.anthropic.com/",
                            "icon": "🧠"
                        }
                    }

                    # 当前配置状态
                    current_config = self._load_current_config()

                    config_status_html = self._generate_config_status_html(current_config, models_config)
                    config_status_display = gr.HTML(config_status_html)

                    # 模型选择
                    model_select = gr.Dropdown(
                        label="选择要配置的模型",
                        choices=[(info["name"], key) for key, info in models_config.items()],
                        value=None
                    )

                    # API密钥输入
                    api_key_input = gr.Textbox(
                        label="API密钥",
                        placeholder="输入API密钥",
                        type="password",
                        visible=False
                    )

                    # 获取密钥链接
                    get_key_link = gr.HTML("", visible=False)

                    # 操作按钮
                    with gr.Row(visible=False) as config_buttons:
                        test_config_btn = gr.Button("🔍 测试连接", variant="secondary")
                        save_config_btn = gr.Button("💾 保存配置", variant="primary")
                        clear_config_btn = gr.Button("🗑️ 清除配置", variant="stop")

                    # 配置结果
                    config_result = gr.Textbox(
                        label="配置结果",
                        interactive=False,
                        visible=False
                    )

                    # 关闭按钮
                    close_config_btn = gr.Button("❌ 关闭", variant="secondary")

            # PDF预览函数
            def preview_pdf(pdf_file):
                """生成PDF预览"""
                if not pdf_file:
                    return "<p style='text-align: center; color: #666;'>📄 上传PDF文件后将显示预览</p>"

                try:
                    import fitz  # PyMuPDF
                    import base64
                    import io
                    from PIL import Image

                    # 打开PDF文件
                    doc = fitz.open(pdf_file)
                    total_pages = len(doc)

                    # 生成前3页的预览图片
                    preview_html = f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                        <h3 style="margin-top: 0; color: #333;">📄 PDF预览 (共 {total_pages} 页)</h3>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                    """

                    # 最多显示前3页
                    max_preview_pages = min(3, total_pages)

                    for page_num in range(max_preview_pages):
                        page = doc[page_num]

                        # 渲染页面为图片
                        mat = fitz.Matrix(1.5, 1.5)  # 缩放因子
                        pix = page.get_pixmap(matrix=mat)
                        img_data = pix.tobytes("png")

                        # 转换为base64
                        img_base64 = base64.b64encode(img_data).decode()

                        preview_html += f"""
                        <div style="text-align: center; margin: 5px;">
                            <p style="margin: 5px 0; font-weight: bold; color: #555;">第 {page_num + 1} 页</p>
                            <img src="data:image/png;base64,{img_base64}"
                                 style="max-width: 200px; max-height: 280px; border: 1px solid #ccc; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" />
                        </div>
                        """

                    if total_pages > 3:
                        preview_html += f"""
                        <div style="text-align: center; margin: 10px; color: #666;">
                            <p>... 还有 {total_pages - 3} 页</p>
                        </div>
                        """

                    preview_html += """
                        </div>
                        <p style="margin-bottom: 0; color: #666; font-size: 12px; text-align: center;">
                            💡 提示：可以设置页码范围来处理特定页面
                        </p>
                    </div>
                    """

                    doc.close()
                    return preview_html

                except Exception as e:
                    return f"""
                    <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                        <h3 style="margin-top: 0; color: #c62828;">❌ PDF预览失败</h3>
                        <p style="color: #666;">错误信息: {str(e)}</p>
                        <p style="color: #666;">请确保上传的是有效的PDF文件</p>
                    </div>
                    """

            # 处理函数
            def process_wrapper(pdf_file, model_type, start_page, end_page, progress=gr.Progress()):
                if not pdf_file:
                    return None, "请先上传PDF文件", None, None, gr.update(visible=False)

                if not model_type or model_type == "none":
                    return None, "请选择AI模型", None, None, gr.update(visible=False)

                if start_page > end_page:
                    return None, "起始页码不能大于结束页码", None, None, gr.update(visible=False)

                # 运行异步处理
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    csv_path, log_message, details = loop.run_until_complete(
                        self.process_pdf(pdf_file, int(start_page), int(end_page), model_type, progress)
                    )

                    # 格式化处理详情
                    details_update = gr.update(visible=False)
                    preview_update = gr.update(visible=False)

                    if details:
                        details_data = [
                            [
                                d["page"],
                                d["status"],
                                self.ai_processor.supported_models.get(d["model"], d["model"]),
                                round(d.get("duration", 0), 2),
                                d.get("extracted_items", 0),
                                d.get("result_length", 0)
                            ]
                            for d in details
                        ]
                        details_update = gr.update(value=details_data, visible=True)

                        # 生成预览数据 - 显示更多行以查看完整结果
                        if csv_path:
                            try:
                                import pandas as pd
                                df = pd.read_csv(csv_path, encoding='utf-8-sig')
                                # 显示所有数据，最多100行
                                max_preview_rows = min(len(df), 100)
                                preview_data = df.head(max_preview_rows).values.tolist()
                                preview_update = gr.update(value=preview_data, visible=True)
                                print(f"预览数据：显示 {max_preview_rows}/{len(df)} 行")
                            except Exception as e:
                                print(f"生成预览失败: {e}")

                    return csv_path, log_message, details_update, preview_update, gr.update(visible=bool(csv_path))
                finally:
                    loop.close()

            # 刷新模型列表函数
            def refresh_models():
                """刷新可用模型列表"""
                # 重新初始化AI处理器以检测新配置
                self.ai_processor = AIModelProcessor()
                available_models = self.ai_processor.get_available_models()

                if not available_models:
                    available_models = {"none": "无可用模型 (请配置API密钥)"}

                choices = list(available_models.items())
                value = list(available_models.keys())[0] if available_models else None

                # 更新模型状态文本
                status_text_update = self._get_model_status_text()

                return gr.update(choices=choices, value=value), status_text_update

            # 模型配置对话框函数
            def show_config_dialog():
                """显示配置对话框"""
                return gr.update(visible=True)

            def hide_config_dialog():
                """隐藏配置对话框"""
                return gr.update(visible=False)

            def on_model_select(selected_model):
                """当选择模型时显示配置选项"""
                if not selected_model:
                    return (
                        gr.update(visible=False),
                        gr.update(visible=False),
                        gr.update(visible=False),
                        gr.update(visible=False)
                    )

                model_info = models_config[selected_model]
                current_key = current_config.get(model_info["env_key"], "")

                return (
                    gr.update(visible=True, value=current_key),
                    gr.update(visible=True, value=f'<p>获取API密钥: <a href="{model_info["url"]}" target="_blank">{model_info["url"]}</a></p>'),
                    gr.update(visible=True),
                    gr.update(visible=False)
                )

            def test_model_config(selected_model, api_key):
                """测试模型配置"""
                if not selected_model or not api_key:
                    return gr.update(visible=True, value="❌ 请选择模型并输入API密钥")

                try:
                    model_info = models_config[selected_model]
                    env_key = model_info["env_key"]

                    # 临时设置API密钥
                    old_value = os.environ.get(env_key)
                    os.environ[env_key] = api_key

                    # 测试连接
                    processor = AIModelProcessor()
                    available_models = processor.get_available_models()

                    # 恢复原值
                    if old_value:
                        os.environ[env_key] = old_value
                    elif env_key in os.environ:
                        del os.environ[env_key]

                    if selected_model in available_models:
                        return gr.update(visible=True, value="✅ 连接测试成功！")
                    else:
                        return gr.update(visible=True, value="❌ 连接测试失败，请检查API密钥")

                except Exception as e:
                    return gr.update(visible=True, value=f"❌ 测试失败: {str(e)}")

            def save_model_config(selected_model, api_key):
                """保存模型配置"""
                if not selected_model or not api_key:
                    return gr.update(visible=True, value="❌ 请选择模型并输入API密钥")

                try:
                    model_info = models_config[selected_model]
                    env_key = model_info["env_key"]

                    # 保存到.env文件
                    success = self._save_api_key(env_key, api_key)

                    if success:
                        # 更新配置状态显示
                        updated_config = self._load_current_config()
                        status_html = self._generate_config_status_html(updated_config, models_config)

                        return (
                            gr.update(visible=True, value="✅ 配置保存成功！"),
                            gr.update(value=status_html)
                        )
                    else:
                        return (
                            gr.update(visible=True, value="❌ 保存失败"),
                            gr.update()
                        )

                except Exception as e:
                    return (
                        gr.update(visible=True, value=f"❌ 保存失败: {str(e)}"),
                        gr.update()
                    )

            def clear_model_config(selected_model):
                """清除模型配置"""
                if not selected_model:
                    return gr.update(visible=True, value="❌ 请选择模型")

                try:
                    model_info = models_config[selected_model]
                    env_key = model_info["env_key"]

                    # 清除配置
                    success = self._save_api_key(env_key, "")

                    if success:
                        # 更新配置状态显示
                        updated_config = self._load_current_config()
                        status_html = self._generate_config_status_html(updated_config, models_config)

                        return (
                            gr.update(visible=True, value="✅ 配置已清除"),
                            gr.update(value=""),
                            gr.update(value=status_html)
                        )
                    else:
                        return (
                            gr.update(visible=True, value="❌ 清除失败"),
                            gr.update(),
                            gr.update()
                        )

                except Exception as e:
                    return (
                        gr.update(visible=True, value=f"❌ 清除失败: {str(e)}"),
                        gr.update(),
                        gr.update()
                    )

            # 绑定事件
            # PDF上传预览
            pdf_input.change(
                fn=preview_pdf,
                inputs=[pdf_input],
                outputs=[pdf_preview]
            )

            process_btn.click(
                fn=process_wrapper,
                inputs=[pdf_input, model_dropdown, start_page, end_page],
                outputs=[csv_output, status_text, processing_details, extraction_preview, csv_output],
                show_progress=True
            )

            refresh_models_btn.click(
                fn=refresh_models,
                outputs=[model_dropdown, model_status]
            )

            config_models_btn.click(
                fn=show_config_dialog,
                outputs=[config_dialog]
            )

            close_config_btn.click(
                fn=hide_config_dialog,
                outputs=[config_dialog]
            )

            # 模型配置对话框事件绑定
            model_select.change(
                fn=on_model_select,
                inputs=[model_select],
                outputs=[api_key_input, get_key_link, config_buttons, config_result]
            )

            test_config_btn.click(
                fn=test_model_config,
                inputs=[model_select, api_key_input],
                outputs=[config_result]
            )

            save_config_btn.click(
                fn=save_model_config,
                inputs=[model_select, api_key_input],
                outputs=[config_result, config_status_display]
            )

            clear_config_btn.click(
                fn=clear_model_config,
                inputs=[model_select],
                outputs=[config_result, api_key_input, config_status_display]
            )

        return interface

    def _load_current_config(self) -> dict:
        """加载当前配置"""
        import os
        from dotenv import load_dotenv

        # 加载.env文件
        load_dotenv()

        config = {}
        env_keys = [
            "OPENAI_API_KEY",
            "ANTHROPIC_API_KEY",
            "GOOGLE_API_KEY",
            "DEEPSEEK_API_KEY",
            "DASHSCOPE_API_KEY"
        ]

        for key in env_keys:
            value = os.environ.get(key, "")
            config[key] = value

        return config

    def _generate_config_status_html(self, current_config: dict, models_config: dict) -> str:
        """生成配置状态HTML"""
        configured_count = len([k for k, v in current_config.items() if v])
        total_count = len(models_config)

        status_html = f"""
        <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3>📊 配置状态</h3>
            <p><strong>已配置模型</strong>: {configured_count} / {total_count}</p>
        """

        for model_key, model_info in models_config.items():
            env_key = model_info["env_key"]
            is_configured = bool(current_config.get(env_key))
            status_icon = "✅" if is_configured else "❌"
            status_html += f"<p>{status_icon} {model_info['name']}</p>"

        status_html += "</div>"
        return status_html

    def _save_api_key(self, env_key: str, api_key: str) -> bool:
        """保存API密钥到.env文件"""
        try:
            import os
            from dotenv import load_dotenv, set_key

            env_file = ".env"

            # 确保.env文件存在
            if not os.path.exists(env_file):
                with open(env_file, 'w') as f:
                    f.write("")

            # 设置环境变量
            set_key(env_file, env_key, api_key)

            # 同时设置到当前环境
            os.environ[env_key] = api_key

            return True
        except Exception as e:
            print(f"保存API密钥失败: {e}")
            return False

    def _get_model_status_text(self) -> str:
        """获取模型状态文本"""
        available_models = self.ai_processor.get_available_models()

        if not available_models:
            return """❌ 无可用模型

请配置以下环境变量:
• OPENAI_API_KEY
• ANTHROPIC_API_KEY
• GOOGLE_API_KEY
• DEEPSEEK_API_KEY

或启动本地Ollama服务"""

        status_lines = ["✅ 可用模型:"]
        for model_id, model_name in available_models.items():
            status_lines.append(f"• {model_name}")

        return "\n".join(status_lines)

def main():
    """主函数"""
    app = QuotaExtractionApp()
    interface = app.create_interface()
    
    # 启动界面
    interface.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False,
        debug=True
    )

if __name__ == "__main__":
    main()
