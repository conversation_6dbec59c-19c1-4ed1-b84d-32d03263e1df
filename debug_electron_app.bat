@echo off
chcp 65001 >nul
echo 🔧 Electron应用调试启动脚本
echo ================================================

echo.
echo 📋 检查环境...

:: 检查Python
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python
    pause
    exit /b 1
)

echo ✅ Python已安装

:: 检查main.py
if not exist "main.py" (
    echo ❌ 错误: 未找到main.py文件
    pause
    exit /b 1
)

echo ✅ main.py文件存在

echo.
echo 🐍 测试Python后端启动...

:: 启动Python服务进行测试
echo 启动Python服务...
start /b py main.py

:: 等待服务启动
timeout /t 10 /nobreak >nul

:: 检查服务是否启动
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:7863' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ Python服务启动成功' } else { Write-Host '❌ Python服务响应异常' } } catch { Write-Host '❌ 无法连接到Python服务' }"

echo.
echo 🖥️ 启动Electron应用...

:: 启动便携版应用
if exist "electron\dist\北京定额提取工具-1.0.0-portable.exe" (
    echo 启动便携版应用...
    start "" "electron\dist\北京定额提取工具-1.0.0-portable.exe"
) else (
    echo ❌ 错误: 便携版应用不存在，请先构建
    pause
    exit /b 1
)

echo.
echo 💡 调试信息:
echo   - Python服务地址: http://localhost:7863
echo   - 如果应用无法启动，请检查Python服务是否正常运行
echo   - 可以手动访问上述地址测试Python服务

pause
