#!/usr/bin/env python3
"""
测试连接功能验证脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_connection_method():
    """测试连接方法"""
    print("🧪 测试连接功能验证")
    print("=" * 50)
    
    try:
        # 导入主应用类
        from main import QuotaExtractionApp
        
        # 创建应用实例
        app = QuotaExtractionApp()
        
        print("✅ 应用实例创建成功")
        
        # 测试不同的连接场景
        test_cases = [
            {
                "name": "无API密钥测试",
                "service": "qwen_qvq_max",
                "api_key": "",
                "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "model": "qvq-max"
            },
            {
                "name": "无效API密钥测试",
                "service": "deepseek_api", 
                "api_key": "sk-invalid-key-test",
                "api_url": "https://api.deepseek.com/v1",
                "model": "deepseek-chat"
            },
            {
                "name": "自定义模型测试",
                "service": "custom_openai",
                "api_key": "test-key",
                "api_url": "http://localhost:1234/v1",
                "model": "test-model"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例 {i}: {test_case['name']}")
            print("-" * 30)
            
            try:
                result = await app._test_model_connection(
                    test_case["service"],
                    test_case["api_key"],
                    test_case["api_url"],
                    test_case["model"]
                )
                
                print(f"🎯 服务: {result.get('service_name', 'N/A')}")
                print(f"✅ 成功: {result.get('success', False)}")
                
                if result.get('success'):
                    print(f"🤖 模型: {result.get('model', 'N/A')}")
                    print(f"📝 响应长度: {result.get('response_length', 0)}")
                    if result.get('response_preview'):
                        print(f"💬 响应预览: {result['response_preview'][:50]}...")
                else:
                    print(f"❌ 错误: {result.get('error', 'N/A')}")
                    if result.get('suggestion'):
                        print(f"💡 建议: {result['suggestion']}")
                        
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        print(f"\n✅ 连接测试功能验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_ui_integration():
    """测试UI集成"""
    print(f"\n🎨 测试UI集成")
    print("=" * 30)
    
    try:
        # 检查模态框相关代码
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键组件
        checks = [
            ("test_connection_btn", "测试连接按钮"),
            ("test_api_connection", "测试连接函数"),
            ("_test_model_connection", "底层测试方法"),
            ("config_modal", "配置模态框"),
            ("api_service", "API服务选择")
        ]
        
        for component, description in checks:
            if component in content:
                print(f"✅ {description}: 已找到")
            else:
                print(f"❌ {description}: 未找到")
        
        print(f"\n✅ UI集成检查完成")
        return True
        
    except Exception as e:
        print(f"❌ UI集成检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试连接功能完整验证")
    print("=" * 60)
    
    # 测试连接方法
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        connection_test = loop.run_until_complete(test_connection_method())
    finally:
        loop.close()
    
    # 测试UI集成
    ui_test = test_ui_integration()
    
    print(f"\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print(f"   🔗 连接测试功能: {'✅ 通过' if connection_test else '❌ 失败'}")
    print(f"   🎨 UI集成: {'✅ 通过' if ui_test else '❌ 失败'}")
    
    if connection_test and ui_test:
        print(f"\n🎉 测试连接功能已完全实现并可用！")
        print(f"💡 现在可以在界面中点击'🔄 测试连接'按钮验证API配置")
    else:
        print(f"\n⚠️ 部分功能需要进一步检查")

if __name__ == "__main__":
    main()
