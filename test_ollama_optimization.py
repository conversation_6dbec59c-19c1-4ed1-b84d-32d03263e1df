#!/usr/bin/env python3
"""
测试Ollama优化和超时处理
"""

import asyncio
import requests
import time
from src.ai_model_processor import AIModelProcessor

def test_ollama_connection():
    """测试Ollama连接状态"""
    
    print("🧪 测试Ollama连接和状态")
    print("=" * 60)
    
    try:
        # 测试基本连接
        print("📡 测试Ollama服务连接...")
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        
        if response.status_code == 200:
            print("✅ Ollama服务连接正常")
            
            data = response.json()
            models = [model["name"] for model in data.get("models", [])]
            print(f"📋 可用模型数量: {len(models)}")
            
            # 显示视觉模型
            vision_models = [m for m in models if any(keyword in m.lower() for keyword in ['vl', 'vision', 'llava', 'qwen'])]
            print(f"🔍 视觉模型:")
            for model in vision_models[:5]:  # 显示前5个
                print(f"  • {model}")
            
            return True, models
        else:
            print(f"❌ Ollama服务响应错误: {response.status_code}")
            return False, []
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Ollama服务")
        print("💡 请确保Ollama正在运行: ollama serve")
        return False, []
    except Exception as e:
        print(f"❌ Ollama连接测试失败: {e}")
        return False, []

def test_ollama_performance():
    """测试Ollama性能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试Ollama性能和负载")
    print("=" * 60)
    
    try:
        # 测试简单文本生成
        print("📝 测试简单文本生成...")
        
        data = {
            "model": "qwen2.5vl:7b",  # 使用常见的视觉模型
            "prompt": "Hello, how are you?",
            "stream": False
        }
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=data,
            timeout=30
        )
        end_time = time.time()
        
        if response.status_code == 200:
            duration = end_time - start_time
            result = response.json()
            response_text = result.get("response", "")
            
            print(f"✅ 文本生成成功")
            print(f"⏱️ 响应时间: {duration:.2f} 秒")
            print(f"📝 响应长度: {len(response_text)} 字符")
            
            # 性能评估
            if duration < 10:
                print("🚀 性能良好")
            elif duration < 30:
                print("⚠️ 性能一般，可能需要优化")
            else:
                print("🐌 性能较慢，建议检查系统资源")
            
            return True
        else:
            print(f"❌ 文本生成失败: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 文本生成超时")
        print("💡 Ollama可能负载过重或模型响应慢")
        return False
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

async def test_ollama_image_processing():
    """测试Ollama图像处理优化"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试Ollama图像处理优化")
    print("=" * 60)
    
    # 创建AI处理器
    processor = AIModelProcessor()
    
    # 创建测试图片
    from PIL import Image
    import tempfile
    import os
    
    # 创建不同大小的测试图片
    test_cases = [
        (100, 100, "小图片"),
        (800, 600, "中等图片"),
        (1920, 1080, "大图片")
    ]
    
    for width, height, description in test_cases:
        print(f"\n🖼️ 测试 {description} ({width}x{height}):")
        
        # 创建测试图片
        test_image = Image.new('RGB', (width, height), color='white')
        
        # 添加一些文本
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(test_image)
            draw.text((10, 10), "Test Image for OCR", fill='black')
        except:
            pass  # 如果没有字体，跳过文本
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            test_image.save(tmp_file.name)
            test_image_path = tmp_file.name
        
        try:
            # 检查图片大小
            file_size = os.path.getsize(test_image_path)
            print(f"  📏 文件大小: {file_size / 1024:.2f} KB")
            
            # 测试base64编码大小
            base64_image = processor._encode_image_to_base64(test_image_path)
            base64_size = len(base64_image)
            print(f"  📦 Base64大小: {base64_size / 1024:.2f} KB")
            
            # 测试压缩
            if base64_size > 1024 * 1024:  # 如果超过1MB
                print(f"  🗜️ 测试图片压缩...")
                compressed_base64 = processor._compress_image_base64(test_image_path)
                compressed_size = len(compressed_base64)
                print(f"  📦 压缩后大小: {compressed_size / 1024:.2f} KB")
                print(f"  📉 压缩比: {(1 - compressed_size / base64_size) * 100:.1f}%")
            
            print(f"  ✅ 图片处理测试完成")
            
        except Exception as e:
            print(f"  ❌ 图片处理测试失败: {e}")
        finally:
            # 清理测试图片
            if os.path.exists(test_image_path):
                os.unlink(test_image_path)
    
    return True

def provide_ollama_optimization_tips():
    """提供Ollama优化建议"""
    
    print(f"\n" + "=" * 60)
    print("💡 Ollama优化建议")
    print("=" * 60)
    
    print("🚀 性能优化:")
    print("1. 确保有足够的系统内存 (推荐16GB+)")
    print("2. 使用SSD存储以提高模型加载速度")
    print("3. 关闭不必要的后台程序")
    print("4. 考虑使用GPU加速 (如果支持)")
    
    print(f"\n⚙️ Ollama配置优化:")
    print("1. 设置环境变量 OLLAMA_NUM_PARALLEL=1 (减少并发)")
    print("2. 设置环境变量 OLLAMA_MAX_LOADED_MODELS=1 (限制加载模型数)")
    print("3. 使用较小的模型 (如7B而不是30B)")
    
    print(f"\n🖼️ 图片处理优化:")
    print("1. 系统会自动压缩大于10MB的图片")
    print("2. 建议PDF转换时使用适中的DPI (150-300)")
    print("3. 避免处理过大的图片 (建议小于5MB)")
    
    print(f"\n🔄 重试机制:")
    print("1. 系统会自动重试失败的请求 (最多3次)")
    print("2. 超时时间已增加到5分钟")
    print("3. 如果仍然超时，建议使用LM Studio或API模型")
    
    print(f"\n🎯 推荐替代方案:")
    print("1. 使用 LM Studio: monkeyocr-recognition (专业OCR)")
    print("2. 使用 阿里通义千问-QVQ-Max (云端API)")
    print("3. 使用较小的Ollama模型进行测试")

if __name__ == "__main__":
    print("🚀 开始测试Ollama优化")
    print("=" * 80)
    
    # 测试Ollama连接
    connection_ok, models = test_ollama_connection()
    
    # 测试性能
    if connection_ok:
        performance_ok = test_ollama_performance()
    else:
        performance_ok = False
    
    # 测试图像处理优化
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        image_processing_ok = loop.run_until_complete(test_ollama_image_processing())
    finally:
        loop.close()
    
    # 提供优化建议
    provide_ollama_optimization_tips()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- Ollama连接: {'✅ 正常' if connection_ok else '❌ 异常'}")
    print(f"- 性能测试: {'✅ 通过' if performance_ok else '❌ 失败'}")
    print(f"- 图像处理: {'✅ 优化完成' if image_processing_ok else '❌ 需要检查'}")
    
    if connection_ok and performance_ok:
        print("🎉 Ollama优化完成！现在应该能更好地处理图片了。")
    else:
        print("⚠️ 建议使用LM Studio或API模型作为替代方案。")
