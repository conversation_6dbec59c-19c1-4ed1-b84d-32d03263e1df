#!/usr/bin/env python3
"""
测试档案管理功能
"""

import os
import pandas as pd
from datetime import datetime
from src.mcp_file_merger import MCPFileMerger

def create_test_csv_files():
    """创建测试CSV文件"""
    
    print("🧪 创建测试CSV文件")
    print("=" * 60)
    
    # 确保output目录存在
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建测试数据
    test_files = []
    
    # 文件1: 第1-5页的定额数据
    data1 = {
        '类型': ['定额项', '定额项', '资源消耗', '资源消耗'],
        '编号': ['1-1', '1-2', 'R001', 'R002'],
        '名称': ['人工挖一般土方 一、二类土', '人工挖一般土方 三、四类土', '综合工日', '材料费'],
        '资源编号': ['', '', '010101', '020101'],
        '类别': ['土石方工程', '土石方工程', '人工', '材料'],
        '消耗量': [1.0, 1.2, 0.8, 150.5],
        '单位': ['100m³', '100m³', '工日', '元']
    }
    df1 = pd.DataFrame(data1)
    file1_path = os.path.join(output_dir, "quota_page_1_5.csv")
    df1.to_csv(file1_path, index=False, encoding='utf-8-sig')
    test_files.append(file1_path)
    print(f"✅ 创建文件: {file1_path} ({len(df1)} 行)")
    
    # 文件2: 第6-10页的定额数据
    data2 = {
        '类型': ['定额项', '定额项', '资源消耗', '资源消耗'],
        '编号': ['1-3', '1-4', 'R003', 'R004'],
        '名称': ['机械挖一般土方', '机械挖坚土', '挖掘机台班', '运输费'],
        '资源编号': ['', '', '030101', '040101'],
        '类别': ['土石方工程', '土石方工程', '机械', '其他'],
        '消耗量': [1.0, 1.1, 0.5, 80.0],
        '单位': ['100m³', '100m³', '台班', '元']
    }
    df2 = pd.DataFrame(data2)
    file2_path = os.path.join(output_dir, "quota_page_6_10.csv")
    df2.to_csv(file2_path, index=False, encoding='utf-8-sig')
    test_files.append(file2_path)
    print(f"✅ 创建文件: {file2_path} ({len(df2)} 行)")
    
    # 文件3: 第11-15页的定额数据
    data3 = {
        '类型': ['定额项', '资源消耗', '资源消耗'],
        '编号': ['1-5', 'R005', 'R006'],
        '名称': ['回填土方', '压实机械', '土方材料'],
        '资源编号': ['', '050101', '060101'],
        '类别': ['土石方工程', '机械', '材料'],
        '消耗量': [1.0, 0.3, 120.0],
        '单位': ['100m³', '台班', '元']
    }
    df3 = pd.DataFrame(data3)
    file3_path = os.path.join(output_dir, "quota_page_11_15.csv")
    df3.to_csv(file3_path, index=False, encoding='utf-8-sig')
    test_files.append(file3_path)
    print(f"✅ 创建文件: {file3_path} ({len(df3)} 行)")
    
    return test_files

def test_file_listing():
    """测试文件列表功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试文件列表功能")
    print("=" * 60)
    
    output_dir = "output"
    
    # 获取CSV文件列表
    import glob
    csv_files = glob.glob(os.path.join(output_dir, "*.csv"))
    
    print(f"📁 找到 {len(csv_files)} 个CSV文件:")
    
    for file_path in csv_files:
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        
        size_mb = file_size / 1024 / 1024
        time_str = mod_time.strftime("%Y-%m-%d %H:%M")
        
        print(f"  • {filename} ({size_mb:.2f}MB, {time_str})")
    
    return len(csv_files) > 0

def test_file_preview():
    """测试文件预览功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试文件预览功能")
    print("=" * 60)
    
    output_dir = "output"
    csv_files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print("❌ 没有找到CSV文件")
        return False
    
    # 测试预览第一个文件
    test_file = csv_files[0]
    file_path = os.path.join(output_dir, test_file)
    
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        print(f"📄 预览文件: {test_file}")
        print(f"- 总行数: {len(df)} 行")
        print(f"- 列数: {len(df.columns)} 列")
        print(f"- 文件大小: {os.path.getsize(file_path) / 1024:.2f} KB")
        
        print(f"\n完整数据内容:")
        print(df.to_string(index=True))

        print(f"\n数据类型:")
        for col in df.columns:
            print(f"  • {col}: {df[col].dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预览失败: {e}")
        return False

def test_mcp_file_merger():
    """测试MCP文件合并工具"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试MCP文件合并工具")
    print("=" * 60)
    
    output_dir = "output"
    csv_files = [os.path.join(output_dir, f) for f in os.listdir(output_dir) if f.endswith('.csv')]
    
    if len(csv_files) < 2:
        print("❌ 需要至少2个CSV文件进行合并测试")
        return False
    
    # 测试文件兼容性分析
    merger = MCPFileMerger()
    
    print("🔍 分析文件兼容性:")
    compatibility = merger.analyze_files_compatibility(csv_files)
    
    print(f"- 兼容性: {compatibility['message']}")
    print(f"- 共同列数: {compatibility.get('common_column_count', 0)}")
    print(f"- 总列数: {compatibility.get('total_unique_columns', 0)}")
    
    if compatibility['compatible']:
        print("✅ 文件兼容，可以合并")
    else:
        print("⚠️ 文件结构不完全一致，但仍可尝试合并")
    
    # 测试不同合并模式
    merge_modes = [
        ("by_page", "按页码排序合并"),
        ("by_filename", "按文件名排序合并"),
        ("by_time", "按创建时间排序合并")
    ]
    
    merge_results = []
    
    for mode, description in merge_modes:
        print(f"\n📋 测试合并模式: {description}")
        
        output_filename = f"merged_test_{mode}.csv"
        output_path = os.path.join(output_dir, output_filename)
        
        success, message, stats = merger.merge_csv_files(csv_files, output_path, mode)
        
        if success:
            print(f"✅ {message}")
            print(f"- 合并文件: {output_filename}")
            print(f"- 总行数: {stats['total_rows']}")
            print(f"- 文件大小: {stats['output_size_kb']:.2f} KB")
            merge_results.append(output_path)
        else:
            print(f"❌ {message}")
    
    return len(merge_results) > 0

def test_merge_preview():
    """测试合并预览功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试合并预览功能")
    print("=" * 60)
    
    output_dir = "output"
    csv_files = [os.path.join(output_dir, f) for f in os.listdir(output_dir) if f.endswith('.csv') and not f.startswith('merged_')]
    
    if len(csv_files) < 2:
        print("❌ 需要至少2个原始CSV文件进行预览测试")
        return False
    
    merger = MCPFileMerger()
    
    print("🔍 生成合并预览:")
    preview_html = merger.generate_merge_preview(csv_files, "by_page")
    
    # 检查预览HTML是否包含预期内容
    if "文件合并预览" in preview_html and "兼容性" in preview_html:
        print("✅ 合并预览生成成功")
        print(f"- 预览HTML长度: {len(preview_html)} 字符")
        print("- 包含文件信息和兼容性分析")
        return True
    else:
        print("❌ 合并预览生成失败")
        return False

def test_file_deletion():
    """测试文件删除功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试文件删除功能")
    print("=" * 60)
    
    output_dir = "output"
    
    # 查找测试合并文件
    test_files = [f for f in os.listdir(output_dir) if f.startswith('merged_test_')]
    
    if not test_files:
        print("⚠️ 没有找到测试合并文件，跳过删除测试")
        return True
    
    print(f"🗑️ 删除 {len(test_files)} 个测试文件:")
    
    deleted_count = 0
    for filename in test_files:
        file_path = os.path.join(output_dir, filename)
        try:
            os.remove(file_path)
            print(f"✅ 删除: {filename}")
            deleted_count += 1
        except Exception as e:
            print(f"❌ 删除失败 {filename}: {e}")
    
    print(f"📊 删除结果: {deleted_count}/{len(test_files)} 个文件")
    
    return deleted_count > 0

if __name__ == "__main__":
    print("🚀 开始测试档案管理功能")
    print("=" * 80)
    
    # 创建测试文件
    test_files = create_test_csv_files()
    
    # 测试文件列表
    listing_success = test_file_listing()
    
    # 测试文件预览
    preview_success = test_file_preview()
    
    # 测试MCP文件合并
    merge_success = test_mcp_file_merger()
    
    # 测试合并预览
    merge_preview_success = test_merge_preview()
    
    # 测试文件删除
    deletion_success = test_file_deletion()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- 文件列表: {'✅ 正常' if listing_success else '❌ 异常'}")
    print(f"- 文件预览: {'✅ 正常' if preview_success else '❌ 异常'}")
    print(f"- MCP合并: {'✅ 正常' if merge_success else '❌ 异常'}")
    print(f"- 合并预览: {'✅ 正常' if merge_preview_success else '❌ 异常'}")
    print(f"- 文件删除: {'✅ 正常' if deletion_success else '❌ 异常'}")
    
    all_success = all([listing_success, preview_success, merge_success, merge_preview_success, deletion_success])
    
    if all_success:
        print("🎉 档案管理功能测试全部通过！")
        print("\n📋 功能特性:")
        print("- ✅ 文件列表显示（大小、时间）")
        print("- ✅ 文件内容预览（前10行）")
        print("- ✅ 多文件合并（3种模式）")
        print("- ✅ 合并预览和兼容性分析")
        print("- ✅ 文件删除管理")
        print("- ✅ MCP工具集成")
    else:
        print("⚠️ 部分功能需要进一步检查。")
