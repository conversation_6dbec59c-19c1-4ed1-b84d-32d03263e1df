#!/usr/bin/env python3
"""
测试Electron应用程序
"""

import os
import time
import subprocess
import requests
from pathlib import Path

def test_electron_build():
    """测试Electron构建结果"""
    print("🔍 测试Electron构建结果")
    print("=" * 50)
    
    dist_dir = Path("electron/dist")
    
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 检查生成的文件
    setup_file = None
    portable_file = None
    
    for file in dist_dir.glob("*.exe"):
        if "Setup" in file.name:
            setup_file = file
        elif "portable" in file.name:
            portable_file = file
    
    print("📋 构建文件检查:")
    if setup_file:
        print(f"✅ 安装程序: {setup_file.name}")
        print(f"   大小: {setup_file.stat().st_size / 1024 / 1024:.1f} MB")
    else:
        print("❌ 安装程序: 未找到")
    
    if portable_file:
        print(f"✅ 便携版: {portable_file.name}")
        print(f"   大小: {portable_file.stat().st_size / 1024 / 1024:.1f} MB")
    else:
        print("❌ 便携版: 未找到")
    
    return setup_file is not None and portable_file is not None

def test_python_backend():
    """测试Python后端是否正常"""
    print(f"\n🐍 测试Python后端")
    print("=" * 30)
    
    try:
        # 启动Python后端
        print("启动Python后端服务...")
        process = subprocess.Popen(
            ["py", "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务启动
        max_wait = 30
        for i in range(max_wait):
            try:
                response = requests.get("http://localhost:7863", timeout=2)
                if response.status_code == 200:
                    print(f"✅ Python后端启动成功 (耗时: {i+1}秒)")
                    process.terminate()
                    return True
            except:
                time.sleep(1)
                print(f"等待服务启动... ({i+1}/{max_wait})")
        
        print("❌ Python后端启动超时")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ Python后端测试失败: {e}")
        return False

def test_electron_portable():
    """测试便携版Electron应用"""
    print(f"\n🖥️ 测试便携版应用")
    print("=" * 35)
    
    try:
        dist_dir = Path("electron/dist")
        portable_file = None
        
        for file in dist_dir.glob("*portable*.exe"):
            portable_file = file
            break
        
        if not portable_file:
            print("❌ 便携版文件不存在")
            return False
        
        print(f"📱 便携版文件: {portable_file.name}")
        print(f"📏 文件大小: {portable_file.stat().st_size / 1024 / 1024:.1f} MB")
        
        # 检查文件是否可执行
        if portable_file.exists() and portable_file.suffix == '.exe':
            print("✅ 便携版文件格式正确")
            return True
        else:
            print("❌ 便携版文件格式错误")
            return False
        
    except Exception as e:
        print(f"❌ 便携版测试失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print(f"\n📋 使用说明")
    print("=" * 30)
    
    dist_dir = Path("electron/dist")
    
    print(f"🚀 运行方式:")
    
    # 安装程序
    setup_files = list(dist_dir.glob("*Setup*.exe"))
    if setup_files:
        setup_file = setup_files[0]
        print(f"  1. 安装程序: 双击 {setup_file.name}")
        print(f"     - 完整安装到系统")
        print(f"     - 创建开始菜单快捷方式")
        print(f"     - 创建桌面快捷方式")
    
    # 便携版
    portable_files = list(dist_dir.glob("*portable*.exe"))
    if portable_files:
        portable_file = portable_files[0]
        print(f"  2. 便携版: 双击 {portable_file.name}")
        print(f"     - 免安装直接运行")
        print(f"     - 适合临时使用")
    
    print(f"\n🎯 应用特性:")
    print(f"  • 自动启动Python后端服务")
    print(f"  • 专业的Windows桌面应用界面")
    print(f"  • 完整的AI定额表格识别功能")
    print(f"  • 文件管理和输出功能")
    
    print(f"\n⚙️ 系统要求:")
    print(f"  • Windows 10/11 (x64)")
    print(f"  • Python 3.8+ (已安装)")
    print(f"  • 约200MB磁盘空间")
    
    print(f"\n📞 技术支持:")
    print(f"  • 开发者: Always派智能研究工作室")
    print(f"  • 邮箱: <EMAIL>")

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    print(f"\n🔗 创建桌面快捷方式")
    print("=" * 35)
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        dist_dir = Path("electron/dist")
        portable_files = list(dist_dir.glob("*portable*.exe"))
        
        if not portable_files:
            print("❌ 未找到便携版文件")
            return False
        
        portable_file = portable_files[0]
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "北京定额提取工具.lnk")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = str(portable_file.absolute())
        shortcut.WorkingDirectory = str(portable_file.parent.absolute())
        shortcut.IconLocation = str(portable_file.absolute())
        shortcut.save()
        
        print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
        return True
        
    except ImportError:
        print("⚠️ 需要安装pywin32和winshell模块来创建快捷方式")
        print("   pip install pywin32 winshell")
        return False
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")
        return False

def main():
    """主函数"""
    print("🎉 Electron桌面应用测试")
    print("=" * 60)
    
    # 运行各项测试
    tests = [
        ("构建结果", test_electron_build),
        ("Python后端", test_python_backend),
        ("便携版应用", test_electron_portable)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        results[test_name] = test_func()
    
    # 汇总结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed >= 2:  # 至少构建和便携版测试通过
        print(f"\n🎉 Electron桌面应用构建成功！")
        
        show_usage_instructions()
        
        # 询问是否创建桌面快捷方式
        try:
            choice = input(f"\n是否创建桌面快捷方式? (y/n): ").lower()
            if choice == 'y':
                create_desktop_shortcut()
        except:
            pass
            
    else:
        print(f"\n⚠️ 部分功能需要检查")
        
        if not results.get("构建结果"):
            print(f"💡 请先运行构建: cd electron && npm run build-win")
        
        if not results.get("Python后端"):
            print(f"💡 请检查Python环境和依赖")

if __name__ == "__main__":
    main()
