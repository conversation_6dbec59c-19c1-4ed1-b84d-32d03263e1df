{"version": 3, "file": "nsisLang.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/nsisLang.ts"], "names": [], "mappings": ";;;AAAA,+CAAsC;AACtC,4CAAyF;AACzF,iCAA0B;AAC1B,uCAA+C;AAC/C,qCAA8B;AAC9B,6BAA4B;AAI5B,yCAA6C;AAE7C,MAAM,KAAK,GAAG,IAAA,eAAM,EAAC,uBAAuB,CAAC,CAAA;AAE7C,MAAa,gBAAgB;IAI3B,YAAY,OAAoB;QAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAA;QAE1C,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YACtG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAA;QAC7D,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,wBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAA,sBAAO,EAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAA,wBAAgB,EAAC,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;QAC9H,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;CACF;AAnBD,4CAmBC;AAED,SAAgB,mBAAmB,CAAC,eAAoC,EAAE,gBAAkC;IAC1G,MAAM,MAAM,GAAkB,EAAE,CAAA;IAChC,KAAK,MAAM,cAAc,IAAI,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACpD,IAAI,IAAY,CAAA;QAChB,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YAC/B,IAAI,GAAG,aAAa,CAAA;QACtB,CAAC;aAAM,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YACtC,IAAI,GAAG,aAAa,CAAA;QACtB,CAAC;aAAM,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YACtC,IAAI,GAAG,WAAW,CAAA;QACpB,CAAC;aAAM,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YACtC,IAAI,GAAG,cAAc,CAAA;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;YACrE,IAAI,GAAI,oBAAoB,CAAC,IAAI,CAAC,CAAA;YAClC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAA;YACzD,CAAC;YAED,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACvB,IAAI,GAAG,sBAAsB,CAAA;YAC/B,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,GAAG,CAAC,CAAA;IACpD,CAAC;IAED,eAAe,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;AAC3C,CAAC;AA3BD,kDA2BC;AAED,KAAK,UAAU,mBAAmB,CAAC,IAAY,EAAE,QAA+B;IAC9E,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA;IACvD,MAAM,IAAA,qBAAU,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC5B,OAAO,IAAI,CAAA;AACb,CAAC;AAEM,KAAK,UAAU,2BAA2B,CAAC,KAAa,EAAE,QAA+B,EAAE,eAAoC,EAAE,gBAAkC;IACxK,MAAM,IAAI,GAAG,IAAA,cAAI,EAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;IAC9E,MAAM,YAAY,GAAG,gCAAgC,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACxF,KAAK,CAAC,YAAY,CAAC,CAAA;IACnB,eAAe,CAAC,OAAO,CAAC,MAAM,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAA;AAC5E,CAAC;AALD,kEAKC;AAED,SAAS,gCAAgC,CAAC,QAAa,EAAE,gBAAkC;IACzF,MAAM,MAAM,GAAkB,EAAE,CAAA;IAChC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;IACrD,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,kBAAkB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAA;QAC9C,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;QACxD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnD,MAAM,cAAc,GAAG,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAA;YAE7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;gBACvC,SAAQ;YACV,CAAC;YAED,MAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAA;YACtC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS,sBAAsB,IAAI,EAAE,CAAC,CAAA;YAC3D,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,IAAI,YAAI,CAAC,cAAc,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,CAAA;YACpG,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;QACzC,CAAC;QAED,IAAI,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC3E,KAAK,MAAM,cAAc,IAAI,gBAAgB,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,IAAI,YAAI,CAAC,cAAc,CAAC,KAAK,kBAAkB,GAAG,CAAC,CAAA;YACxF,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import { asArray } from \"builder-util\"\nimport { bundledLanguages, langIdToName, lcid, toLangWithRegion } from \"../../util/langs\"\nimport _debug from \"debug\"\nimport { outputFile, readFile } from \"fs-extra\"\nimport { load } from \"js-yaml\"\nimport * as path from \"path\"\nimport { PlatformPackager } from \"../../platformPackager\"\nimport { NsisOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { nsisTemplatesDir } from \"./nsisUtil\"\n\nconst debug = _debug(\"electron-builder:nsis\")\n\nexport class LangConfigurator {\n  readonly isMultiLang: boolean\n  readonly langs: Array<string>\n\n  constructor(options: NsisOptions) {\n    const rawList = options.installerLanguages\n\n    if (options.unicode === false || rawList === null || (Array.isArray(rawList) && rawList.length === 0)) {\n      this.isMultiLang = false\n    } else {\n      this.isMultiLang = options.multiLanguageInstaller !== false\n    }\n\n    if (this.isMultiLang) {\n      this.langs = rawList == null ? bundledLanguages.slice() : asArray(rawList).map(it => toLangWithRegion(it.replace(\"-\", \"_\")))\n    } else {\n      this.langs = [\"en_US\"]\n    }\n  }\n}\n\nexport function createAddLangsMacro(scriptGenerator: NsisScriptGenerator, langConfigurator: LangConfigurator) {\n  const result: Array<string> = []\n  for (const langWithRegion of langConfigurator.langs) {\n    let name: string\n    if (langWithRegion === \"zh_CN\") {\n      name = \"SimpChinese\"\n    } else if (langWithRegion === \"zh_TW\") {\n      name = \"TradChinese\"\n    } else if (langWithRegion === \"nb_NO\") {\n      name = \"Norwegian\"\n    } else if (langWithRegion === \"pt_BR\") {\n      name = \"PortugueseBR\"\n    } else {\n      const lang = langWithRegion.substring(0, langWithRegion.indexOf(\"_\"))\n      name = (langIdToName as any)[lang]\n      if (name == null) {\n        throw new Error(`Language name is unknown for ${lang}`)\n      }\n\n      if (name === \"Spanish\") {\n        name = \"SpanishInternational\"\n      }\n    }\n    result.push(`!insertmacro MUI_LANGUAGE \"${name}\"`)\n  }\n\n  scriptGenerator.macro(\"addLangs\", result)\n}\n\nasync function writeCustomLangFile(data: string, packager: PlatformPackager<any>) {\n  const file = await packager.getTempFile(\"messages.nsh\")\n  await outputFile(file, data)\n  return file\n}\n\nexport async function addCustomMessageFileInclude(input: string, packager: PlatformPackager<any>, scriptGenerator: NsisScriptGenerator, langConfigurator: LangConfigurator) {\n  const data = load(await readFile(path.join(nsisTemplatesDir, input), \"utf-8\"))\n  const instructions = computeCustomMessageTranslations(data, langConfigurator).join(\"\\n\")\n  debug(instructions)\n  scriptGenerator.include(await writeCustomLangFile(instructions, packager))\n}\n\nfunction computeCustomMessageTranslations(messages: any, langConfigurator: LangConfigurator): Array<string> {\n  const result: Array<string> = []\n  const includedLangs = new Set(langConfigurator.langs)\n  for (const messageId of Object.keys(messages)) {\n    const langToTranslations = messages[messageId]\n    const unspecifiedLangs = new Set(langConfigurator.langs)\n    for (const lang of Object.keys(langToTranslations)) {\n      const langWithRegion = toLangWithRegion(lang)\n\n      if (!includedLangs.has(langWithRegion)) {\n        continue\n      }\n\n      const value = langToTranslations[lang]\n      if (value == null) {\n        throw new Error(`${messageId} not specified for ${lang}`)\n      }\n\n      result.push(`LangString ${messageId} ${lcid[langWithRegion]} \"${value.replace(/\\n/g, \"$\\\\r$\\\\n\")}\"`)\n      unspecifiedLangs.delete(langWithRegion)\n    }\n\n    if (langConfigurator.isMultiLang) {\n      const defaultTranslation = langToTranslations.en.replace(/\\n/g, \"$\\\\r$\\\\n\")\n      for (const langWithRegion of unspecifiedLangs) {\n        result.push(`LangString ${messageId} ${lcid[langWithRegion]} \"${defaultTranslation}\"`)\n      }\n    }\n  }\n  return result\n}\n"]}