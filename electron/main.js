const { app, BrowserWindow, Menu, shell, dialog, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const log = require('electron-log');

// 配置日志
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

class QuotaExtractionApp {
    constructor() {
        this.mainWindow = null;
        this.pythonProcess = null;
        this.serverPort = 7863;
        this.serverUrl = `http://localhost:${this.serverPort}`;
        this.isQuitting = false;
    }

    async createWindow() {
        // 创建主窗口
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            icon: path.join(__dirname, 'assets', 'icon.ico'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js'),
                webSecurity: false // 允许加载本地服务器
            },
            show: false,
            titleBarStyle: 'default',
            autoHideMenuBar: false
        });

        // 设置窗口标题
        this.mainWindow.setTitle('北京市2021消耗定额智能提取工具');

        // 创建菜单
        this.createMenu();

        // 加载启动页面
        await this.mainWindow.loadFile('index.html');

        // 启动Python后端
        await this.startPythonServer();

        // 窗口事件处理
        this.setupWindowEvents();

        // 显示窗口
        this.mainWindow.show();

        log.info('应用启动完成');
    }

    createMenu() {
        const template = [
            {
                label: '文件',
                submenu: [
                    {
                        label: '打开PDF文件',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => {
                            this.openFileDialog();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '退出',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            this.isQuitting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: '工具',
                submenu: [
                    {
                        label: '重新启动服务',
                        click: () => {
                            this.restartPythonServer();
                        }
                    },
                    {
                        label: '打开输出文件夹',
                        click: () => {
                            this.openOutputFolder();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '开发者工具',
                        accelerator: 'F12',
                        click: () => {
                            this.mainWindow.webContents.toggleDevTools();
                        }
                    }
                ]
            },
            {
                label: '帮助',
                submenu: [
                    {
                        label: '关于',
                        click: () => {
                            this.showAboutDialog();
                        }
                    },
                    {
                        label: '联系我们',
                        click: () => {
                            shell.openExternal('mailto:<EMAIL>');
                        }
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    async startPythonServer() {
        return new Promise((resolve, reject) => {
            try {
                const pythonPath = this.getPythonPath();
                const mainPyPath = this.getMainPyPath();

                log.info(`启动Python服务: ${pythonPath} ${mainPyPath}`);
                log.info(`工作目录: ${path.dirname(mainPyPath)}`);
                log.info(`main.py文件是否存在: ${fs.existsSync(mainPyPath)}`);

                // 检查main.py文件是否存在
                if (!fs.existsSync(mainPyPath)) {
                    const error = `main.py文件不存在: ${mainPyPath}`;
                    log.error(error);
                    reject(new Error(error));
                    return;
                }

                // 启动Python进程
                this.pythonProcess = spawn(pythonPath, [mainPyPath], {
                    cwd: path.dirname(mainPyPath),
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                // 处理Python进程输出
                this.pythonProcess.stdout.on('data', (data) => {
                    const output = data.toString();
                    log.info('Python输出:', output);
                    
                    // 检查服务是否启动成功
                    if (output.includes('Running on local URL')) {
                        log.info('Python服务启动成功');
                        setTimeout(() => {
                            this.loadMainApp();
                            resolve();
                        }, 2000);
                    }
                });

                this.pythonProcess.stderr.on('data', (data) => {
                    const error = data.toString();
                    log.error('Python错误:', error);
                });

                this.pythonProcess.on('close', (code) => {
                    log.info(`Python进程退出，代码: ${code}`);
                    if (code !== 0 && !this.isQuitting) {
                        this.showErrorDialog('Python服务异常退出', `退出代码: ${code}`);
                    }
                });

                this.pythonProcess.on('error', (error) => {
                    log.error('Python进程错误:', error);
                    reject(error);
                });

                // 超时处理
                setTimeout(() => {
                    if (this.pythonProcess && !this.pythonProcess.killed) {
                        log.warn('Python服务启动超时，尝试直接连接');
                        this.checkServerAndLoad();
                        resolve();
                    }
                }, 15000);

            } catch (error) {
                log.error('启动Python服务失败:', error);
                reject(error);
            }
        });
    }

    getPythonPath() {
        // 优先使用系统Python
        return process.platform === 'win32' ? 'py' : 'python3';
    }

    getMainPyPath() {
        // 始终使用相对于electron目录的上级目录中的main.py
        return path.join(__dirname, '..', 'main.py');
    }

    async checkServerAndLoad() {
        const maxRetries = 10;
        for (let i = 0; i < maxRetries; i++) {
            try {
                const response = await fetch(this.serverUrl, { timeout: 3000 });
                if (response.ok) {
                    log.info('服务器检查成功，加载主应用');
                    await this.loadMainApp();
                    return;
                }
            } catch (error) {
                log.info(`服务器检查失败 (${i + 1}/${maxRetries}): ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        log.error('服务器检查超时，显示错误');
        this.showErrorDialog('加载失败', '无法连接到Python服务，请检查服务是否正常启动');
    }

    async loadMainApp() {
        try {
            log.info(`加载主应用: ${this.serverUrl}`);
            await this.mainWindow.loadURL(this.serverUrl);

            // 注入自定义样式
            this.mainWindow.webContents.insertCSS(`
                body {
                    margin: 0;
                    padding: 0;
                }
                /* 隐藏Gradio的一些不必要元素 */
                .gradio-container {
                    max-width: none !important;
                }
            `);

        } catch (error) {
            log.error('加载主应用失败:', error);
            this.showErrorDialog('加载失败', '无法连接到Python服务，请检查服务是否正常启动');
        }
    }

    setupWindowEvents() {
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting) {
                event.preventDefault();
                this.mainWindow.hide();
            }
        });

        // 处理外部链接
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });
    }

    async restartPythonServer() {
        log.info('重新启动Python服务');
        
        if (this.pythonProcess) {
            this.pythonProcess.kill();
            this.pythonProcess = null;
        }

        await this.mainWindow.loadFile('index.html');
        await this.startPythonServer();
    }

    openFileDialog() {
        dialog.showOpenDialog(this.mainWindow, {
            properties: ['openFile'],
            filters: [
                { name: 'PDF文件', extensions: ['pdf'] }
            ]
        }).then(result => {
            if (!result.canceled && result.filePaths.length > 0) {
                // 通过IPC发送文件路径到渲染进程
                this.mainWindow.webContents.send('file-selected', result.filePaths[0]);
            }
        });
    }

    openOutputFolder() {
        const outputPath = app.isPackaged 
            ? path.join(process.resourcesPath, 'python', 'output')
            : path.join(__dirname, '..', 'output');
        
        if (fs.existsSync(outputPath)) {
            shell.openPath(outputPath);
        } else {
            dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: '提示',
                message: '输出文件夹不存在',
                detail: '请先处理一些文件以创建输出文件夹'
            });
        }
    }

    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: '关于',
            message: '北京市2021消耗定额智能提取工具',
            detail: `版本: 1.0.0\n\n开发者: Always派智能研究工作室\n联系方式: <EMAIL>\n\n专业的AI驱动定额表格识别工具`
        });
    }

    showErrorDialog(title, message) {
        const options = {
            type: 'error',
            title: title,
            message: message,
            detail: '请尝试以下解决方案:\n\n1. 手动启动Python服务:\n   - 打开命令行\n   - 进入应用目录\n   - 运行: py main.py\n\n2. 检查Python环境:\n   - 确保Python已正确安装\n   - 确保所有依赖已安装\n\n3. 联系技术支持:\n   - 邮箱: <EMAIL>',
            buttons: ['重试', '手动启动说明', '退出']
        };

        dialog.showMessageBox(this.mainWindow, options).then((response) => {
            if (response.response === 0) {
                // 重试
                this.restartPythonServer();
            } else if (response.response === 1) {
                // 显示手动启动说明
                this.showManualStartInstructions();
            } else {
                // 退出
                app.quit();
            }
        });
    }

    showManualStartInstructions() {
        const instructions = `手动启动Python服务步骤:

1. 打开命令提示符 (cmd)
2. 进入应用目录
3. 运行命令: py main.py
4. 等待服务启动 (显示 "Running on local URL")
5. 重新启动此应用

服务地址: http://localhost:7863

如果仍有问题，请联系技术支持:
邮箱: <EMAIL>`;

        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: '手动启动说明',
            message: '手动启动Python服务',
            detail: instructions,
            buttons: ['确定', '重试连接']
        }).then((response) => {
            if (response.response === 1) {
                this.checkServerAndLoad();
            }
        });
    }

    cleanup() {
        log.info('清理资源');
        
        if (this.pythonProcess) {
            this.pythonProcess.kill();
            this.pythonProcess = null;
        }
    }
}

// 应用实例
const quotaApp = new QuotaExtractionApp();

// 应用事件处理
app.whenReady().then(() => {
    quotaApp.createWindow();
});

app.on('window-all-closed', () => {
    quotaApp.cleanup();
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        quotaApp.createWindow();
    } else if (quotaApp.mainWindow) {
        quotaApp.mainWindow.show();
    }
});

app.on('before-quit', () => {
    quotaApp.isQuitting = true;
    quotaApp.cleanup();
});

// IPC处理
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('get-app-path', () => {
    return app.getAppPath();
});
