#!/usr/bin/env python3
"""
AI模型处理器 - 支持多种AI模型API进行图像识别
替代浏览器自动化方案
"""

import asyncio
import base64
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
import requests
import time
import urllib3

# 禁用SSL警告（用于解决SSL问题）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None

try:
    import google.generativeai as genai
except ImportError:
    genai = None

from .config import Config

class AIModelProcessor:
    """AI模型处理器"""
    
    def __init__(self):
        self.config = Config()
        self.supported_models = {
            "openai_gpt4v": "OpenAI GPT-4 Vision",
            "claude_vision": "Anthropic Claude Vision",
            "gemini_vision": "Google Gemini Vision",
            "deepseek_api": "DeepSeek API",
            "qwen_qvq_max": "阿里通义千问-QVQ-Max",
            "qwen_qvq_plus": "阿里通义千问-QVQ-Plus",
            "local_ollama": "本地Ollama模型",
            "lm_studio": "LM Studio本地模型"
        }
        
        # 从环境变量或配置文件加载API密钥
        self.api_keys = {
            "openai": os.getenv("OPENAI_API_KEY"),
            "anthropic": os.getenv("ANTHROPIC_API_KEY"),
            "google": os.getenv("GOOGLE_API_KEY"),
            "deepseek": os.getenv("DEEPSEEK_API_KEY"),
            "dashscope": os.getenv("DASHSCOPE_API_KEY")  # 阿里云百炼API密钥
        }
        
        # 初始化客户端
        self.clients = {}
        self._initialize_clients()
        
        # 配置网络请求session
        self.session = requests.Session()
        self._configure_session()
    
    def _configure_session(self):
        """配置网络请求session，处理代理和SSL问题"""
        # 配置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 检查并处理代理设置
        # 移除可能导致问题的代理设置
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        if 'HTTP_PROXY' in os.environ:
            del os.environ['HTTP_PROXY']
        if 'HTTPS_PROXY' in os.environ:
            del os.environ['HTTPS_PROXY']
            
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # SSL配置 - 对于有SSL问题的情况，可以临时禁用SSL验证
        # 注意：这仅用于调试，生产环境建议修复SSL配置
        self.session.verify = True  # 默认启用SSL验证
        
    def _make_request_with_retry(self, url, **kwargs):
        """带重试的网络请求"""
        max_retries = 3
        backoff_factor = 2
        
        for attempt in range(max_retries):
            try:
                # 如果SSL验证失败，尝试禁用SSL验证
                if attempt > 0:
                    kwargs['verify'] = False
                    print(f"第{attempt + 1}次重试，尝试禁用SSL验证...")
                
                response = self.session.request(**kwargs, url=url)
                return response
            except requests.exceptions.SSLError as e:
                print(f"SSL错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except requests.exceptions.ProxyError as e:
                print(f"代理错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                # 移除代理设置
                if 'proxies' in kwargs:
                    del kwargs['proxies']
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except requests.exceptions.ConnectionError as e:
                print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except Exception as e:
                print(f"请求错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
        
        return None
    
    def _initialize_clients(self):
        """初始化各种AI模型客户端"""
        try:
            # OpenAI客户端
            if openai and self.api_keys["openai"]:
                self.clients["openai"] = openai.OpenAI(
                    api_key=self.api_keys["openai"]
                )
            
            # Anthropic客户端
            if anthropic and self.api_keys["anthropic"]:
                self.clients["anthropic"] = anthropic.Anthropic(
                    api_key=self.api_keys["anthropic"]
                )
            
            # Google Gemini客户端
            if genai and self.api_keys["google"]:
                genai.configure(api_key=self.api_keys["google"])
                self.clients["google"] = genai
                
        except Exception as e:
            print(f"初始化AI客户端时出错: {e}")
    
    def get_available_models(self) -> Dict[str, str]:
        """获取可用的模型列表"""
        available = {}
        
        # 检查各个模型的可用性
        if "openai" in self.clients:
            available["openai_gpt4v"] = self.supported_models["openai_gpt4v"]
        
        if "anthropic" in self.clients:
            available["claude_vision"] = self.supported_models["claude_vision"]
            
        if "google" in self.clients:
            available["gemini_vision"] = self.supported_models["gemini_vision"]
        
        # DeepSeek API (如果有API密钥)
        if self.api_keys["deepseek"]:
            available["deepseek_api"] = self.supported_models["deepseek_api"]

        # 阿里云通义千问-QVQ模型 (如果有API密钥)
        if self.api_keys["dashscope"]:
            available["qwen_qvq_max"] = self.supported_models["qwen_qvq_max"]
            available["qwen_qvq_plus"] = self.supported_models["qwen_qvq_plus"]

        # 本地Ollama (检查是否运行)
        if self._check_ollama_available():
            ollama_models = self.get_ollama_models()
            if ollama_models:
                for model in ollama_models:
                    model_key = f"ollama_{model.replace(':', '_').replace('-', '_')}"
                    available[model_key] = f"Ollama: {model}"
            else:
                available["local_ollama"] = self.supported_models["local_ollama"]

        # LM Studio (检查是否运行)
        if self._check_lm_studio_available():
            lm_studio_models = self.get_lm_studio_models()
            if lm_studio_models:
                for model in lm_studio_models:
                    model_key = f"lm_studio_{model.replace('/', '_').replace('-', '_')}"
                    available[model_key] = f"LM Studio: {model}"
            else:
                available["lm_studio"] = self.supported_models["lm_studio"]

        return available
    
    def _check_ollama_available(self) -> bool:
        """检查本地Ollama是否可用"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_ollama_models(self) -> List[str]:
        """获取可用的Ollama模型列表"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = [model["name"] for model in data.get("models", [])]
                return models
            return []
        except:
            return []

    def _check_lm_studio_available(self) -> bool:
        """检查LM Studio是否可用"""
        try:
            response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_lm_studio_models(self) -> List[str]:
        """获取可用的LM Studio模型列表"""
        try:
            response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = [model["id"] for model in data.get("data", [])]
                return models
            return []
        except:
            return []
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    async def process_image_with_openai(self, image_path: str) -> Optional[str]:
        """使用OpenAI GPT-4V处理图片"""
        try:
            if "openai" not in self.clients:
                return None
            
            base64_image = self._encode_image_to_base64(image_path)
            
            response = self.clients["openai"].chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.config.QUOTA_EXTRACTION_PROMPT
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=4000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"OpenAI处理失败: {e}")
            return None
    
    async def process_image_with_claude(self, image_path: str) -> Optional[str]:
        """使用Claude Vision处理图片"""
        try:
            if "anthropic" not in self.clients:
                return None
            
            base64_image = self._encode_image_to_base64(image_path)
            
            response = self.clients["anthropic"].messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=4000,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": base64_image
                                }
                            },
                            {
                                "type": "text",
                                "text": self.config.QUOTA_EXTRACTION_PROMPT
                            }
                        ]
                    }
                ]
            )
            
            return response.content[0].text
            
        except Exception as e:
            print(f"Claude处理失败: {e}")
            return None
    
    async def process_image_with_gemini(self, image_path: str) -> Optional[str]:
        """使用Google Gemini处理图片"""
        try:
            if "google" not in self.clients:
                return None
            
            # 读取图片
            from PIL import Image
            image = Image.open(image_path)
            
            model = self.clients["google"].GenerativeModel('gemini-pro-vision')
            response = model.generate_content([
                self.config.QUOTA_EXTRACTION_PROMPT,
                image
            ])
            
            return response.text
            
        except Exception as e:
            print(f"Gemini处理失败: {e}")
            return None
    
    async def process_image_with_deepseek_api(self, image_path: str) -> Optional[str]:
        """使用DeepSeek API处理图片"""
        try:
            if not self.api_keys["deepseek"]:
                return None
            
            base64_image = self._encode_image_to_base64(image_path)
            
            headers = {
                "Authorization": f"Bearer {self.api_keys['deepseek']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "deepseek-vl-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.config.QUOTA_EXTRACTION_PROMPT
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 4000
            }
            
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                print(f"DeepSeek API错误: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"DeepSeek API处理失败: {e}")
            return None
    
    async def process_image_with_ollama(self, image_path: str, model_name: str = "llava") -> Optional[str]:
        """使用本地Ollama模型处理图片"""
        try:
            base64_image = self._encode_image_to_base64(image_path)

            data = {
                "model": model_name,
                "prompt": self.config.QUOTA_EXTRACTION_PROMPT,
                "images": [base64_image],
                "stream": False
            }

            response = requests.post(
                "http://localhost:11434/api/generate",
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                print(f"Ollama错误: {response.status_code}")
                return None

        except Exception as e:
            print(f"Ollama处理失败: {e}")
            return None

    async def process_image_with_lm_studio(self, image_path: str, model_name: str = "monkeyocr-recognition") -> Optional[str]:
        """使用LM Studio模型处理图片"""
        try:
            base64_image = self._encode_image_to_base64(image_path)

            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.config.QUOTA_EXTRACTION_PROMPT
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.1
            }

            response = requests.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                print(f"LM Studio错误: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"LM Studio处理失败: {e}")
            return None

    async def process_image_with_qwen_qvq(self, image_path: str, model_name: str = "qvq-max") -> Optional[str]:
        """
        使用阿里云通义千问-QVQ模型处理图片

        Args:
            image_path: 图片文件路径
            model_name: 模型名称 (qvq-max 或 qvq-plus)

        Returns:
            Optional[str]: 识别结果
        """
        try:
            if not self.api_keys["dashscope"]:
                return None

            # 将图片编码为base64
            base64_image = self._encode_image_to_base64(image_path)

            # 构建请求数据 - 使用OpenAI兼容格式
            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json"
            }

            # QVQ模型的特殊提示词，针对定额表格优化
            qvq_prompt = f"""
请仔细分析这张北京市消耗定额表格图片。我需要你按照以下步骤进行分析：

1. **识别表格结构**：
   - 确定这是否为标准的北京市消耗定额表格
   - 识别表格上半部分的定额编号列（如：1-11、1-12、1-13）
   - 识别表格下半部分的资源消耗明细

2. **提取每个定额项的信息**（表格上半部分）：
   - 定额编号（如：1-1、1-2、1-3）
   - 定额项名称：需要组合主项名称和差异描述
     * 主项名称（如：人工挖一般土方）
     * 差异描述（如：一、二类土、三类土、四类土）
     * 完整名称（如：人工挖一般土方 一、二类土）
   - 工作内容（如：挖土、余土清理、修整底边等）
   - 单位（如：m³）

3. **提取资源消耗信息**（表格下半部分的每一行）：
   - 资源编号（如：00010701）
   - 类别（如：人工、机械、其他费用）
   - 子项名称（如：综合用工三类、电动打钉机、其他机具费占人工费）
   - 单位（如：工日、台班、%）
   - 消耗量（如：0.200、0.0039、1.50）

4. **特别注意**：
   - 图片中可能有多个定额项（如1-1、1-2、1-3），每个都要单独提取
   - 定额项名称要完整：主项名称 + 差异描述（如：人工挖一般土方 一、二类土）
   - 仔细观察表格中每列的差异描述文字，将其合并到定额项名称中
   - 每个定额项通常有相同的资源消耗明细，但消耗量可能不同
   - 单位为"%"的资源项是特殊的百分比费用项
   - 确保提取所有资源消耗行
   - 保留消耗量的小数精度

请以JSON格式返回结果（支持多个定额项）：
{{
    "quotas": [
        {{
            "parent_quota": {{
                "code": "定额编号",
                "name": "定额项名称",
                "work_content": "工作内容",
                "unit": "单位"
            }},
            "resource_consumption": [
                {{
                    "resource_code": "资源编号",
                    "category": "类别",
                    "name": "子项名称",
                    "unit": "单位",
                    "consumption": "消耗量"
                }}
            ]
        }}
    ]
}}

如果只有一个定额项，quotas数组中只包含一个元素。
如果有多个定额项（如1-11、1-12、1-13），quotas数组中包含多个元素。
"""

            # 模型回退策略 - 如果QVQ模型不可用，使用通用模型
            fallback_models = [model_name, "qwen-vl-max", "qwen-max", "qwen-vl-plus"]
            
            for try_model in fallback_models:
                print(f"尝试使用模型: {try_model}")
                
                data = {
                    "model": try_model,
                    "messages": [
                        {
                            "role": "user", 
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}"
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": qvq_prompt
                                }
                            ]
                        }
                    ],
                    "stream": True if try_model.startswith("qvq") else False  # QVQ模型支持流式，其他不一定
                }

                # 发送请求到阿里云百炼API - 使用重试机制
                response = self._make_request_with_retry(
                    "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                    method='POST',
                    headers=headers,
                    json=data,
                    stream=data["stream"],
                    timeout=120
                )

                if response.status_code == 200:
                    print(f"✅ 模型 {try_model} 连接成功")
                    break
                else:
                    error_text = response.text if hasattr(response, 'text') else str(response)
                    print(f"❌ 模型 {try_model} 失败: {response.status_code} - {error_text[:100]}")
                    if try_model == fallback_models[-1]:  # 最后一个模型也失败了
                        print(f"所有模型都失败了")
                        return None
                    continue
            else:
                print(f"所有模型都失败了") 
                return None

            # 处理响应 - 支持流式和非流式
            if data["stream"]:
                # 处理流式响应 (QVQ模型)
                reasoning_content = ""  # 思考过程
                answer_content = ""     # 最终回答
                is_answering = False    # 是否开始回答阶段

                for line in response.iter_lines():
                    if line:
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_text = line_text[6:]  # 移除 'data: ' 前缀

                            if data_text.strip() == '[DONE]':
                                break

                            try:
                                chunk_data = json.loads(data_text)
                                if 'choices' in chunk_data and chunk_data['choices']:
                                    delta = chunk_data['choices'][0].get('delta', {})

                                    # 处理思考过程
                                    if 'reasoning_content' in delta and delta['reasoning_content']:
                                        reasoning_content += delta['reasoning_content']

                                    # 处理最终回答
                                    elif 'content' in delta and delta['content']:
                                        if not is_answering:
                                            is_answering = True
                                        answer_content += delta['content']

                            except json.JSONDecodeError:
                                continue

                # 返回最终回答内容
                if answer_content:
                    print(f"思考过程长度: {len(reasoning_content)} 字符")
                    print(f"回答内容长度: {len(answer_content)} 字符")
                    return answer_content
                else:
                    print("流式模型未返回有效回答")
                    return None
            else:
                # 处理非流式响应 (通用模型)
                try:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0]['message']['content']
                        print(f"回答内容长度: {len(content)} 字符")
                        return content
                    else:
                        print("非流式模型未返回有效回答")
                        return None
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    return None

        except Exception as e:
            print(f"QVQ模型处理失败: {e}")
            return None
    
    async def process_image(self, image_path: str, model_type: str, **kwargs) -> Optional[str]:
        """
        使用指定模型处理图片
        
        Args:
            image_path: 图片路径
            model_type: 模型类型
            **kwargs: 额外参数
            
        Returns:
            Optional[str]: 识别结果
        """
        print(f"使用 {self.supported_models.get(model_type, model_type)} 处理图片: {image_path}")
        
        try:
            if model_type == "openai_gpt4v":
                return await self.process_image_with_openai(image_path)
            elif model_type == "claude_vision":
                return await self.process_image_with_claude(image_path)
            elif model_type == "gemini_vision":
                return await self.process_image_with_gemini(image_path)
            elif model_type == "deepseek_api":
                return await self.process_image_with_deepseek_api(image_path)
            elif model_type == "qwen_qvq_max":
                return await self.process_image_with_qwen_qvq(image_path, "qvq-max")
            elif model_type == "qwen_qvq_plus":
                return await self.process_image_with_qwen_qvq(image_path, "qvq-plus")
            elif model_type == "local_ollama":
                model_name = kwargs.get("ollama_model", "llava")
                return await self.process_image_with_ollama(image_path, model_name)
            elif model_type.startswith("ollama_"):
                # 处理具体的Ollama模型
                model_name = model_type.replace("ollama_", "").replace("_", ":")
                # 如果模型名不包含版本号，尝试常见的格式
                if ":" not in model_name:
                    model_name = model_name.replace("_", "-")
                return await self.process_image_with_ollama(image_path, model_name)
            elif model_type == "lm_studio":
                return await self.process_image_with_lm_studio(image_path, "monkeyocr-recognition")
            elif model_type.startswith("lm_studio_"):
                # 处理具体的LM Studio模型
                model_name = model_type.replace("lm_studio_", "").replace("_", "/")
                return await self.process_image_with_lm_studio(image_path, model_name)
            else:
                print(f"不支持的模型类型: {model_type}")
                return None
                
        except Exception as e:
            print(f"处理图片时发生错误: {e}")
            return None
