#!/usr/bin/env python3
"""
MCP数据库转换工具
Multi-CSV Processing (MCP) Database Converter for converting CSV files to various database formats
"""

import os
import pandas as pd
import sqlite3
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
import logging

class MCPDatabaseConverter:
    """MCP数据库转换工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 支持的数据库格式
        self.supported_formats = {
            'sqlite': 'SQLite数据库文件',
            'mysql': 'MySQL SQL脚本',
            'postgresql': 'PostgreSQL SQL脚本',
            'sql_server': 'SQL Server SQL脚本',
            'oracle': 'Oracle SQL脚本'
        }
        
        # 数据类型映射
        self.type_mappings = {
            'sqlite': {
                'int64': 'INTEGER',
                'float64': 'REAL',
                'object': 'TEXT',
                'bool': 'INTEGER',
                'datetime64[ns]': 'DATETIME'
            },
            'mysql': {
                'int64': 'INT',
                'float64': 'DECIMAL(10,2)',
                'object': 'VARCHAR(255)',
                'bool': 'BOOLEAN',
                'datetime64[ns]': 'DATETIME'
            },
            'postgresql': {
                'int64': 'INTEGER',
                'float64': 'DECIMAL(10,2)',
                'object': 'VARCHAR(255)',
                'bool': 'BOOLEAN',
                'datetime64[ns]': 'TIMESTAMP'
            },
            'sql_server': {
                'int64': 'INT',
                'float64': 'DECIMAL(10,2)',
                'object': 'NVARCHAR(255)',
                'bool': 'BIT',
                'datetime64[ns]': 'DATETIME'
            },
            'oracle': {
                'int64': 'NUMBER',
                'float64': 'NUMBER(10,2)',
                'object': 'VARCHAR2(255)',
                'bool': 'NUMBER(1)',
                'datetime64[ns]': 'DATE'
            }
        }
    
    def analyze_csv_structure(self, csv_path: str) -> Dict[str, Any]:
        """分析CSV文件结构"""
        try:
            df = pd.read_csv(csv_path, encoding='utf-8-sig')
            
            # 分析数据类型
            column_info = {}
            for col in df.columns:
                dtype = str(df[col].dtype)
                sample_values = df[col].dropna().head(5).tolist()
                null_count = df[col].isnull().sum()
                unique_count = df[col].nunique()
                
                # 检测是否为主键候选
                is_primary_key = (
                    unique_count == len(df) and 
                    null_count == 0 and 
                    dtype in ['int64', 'object']
                )
                
                # 检测文本长度
                max_length = 50
                if dtype == 'object':
                    max_length = max(df[col].astype(str).str.len().max(), 50)
                    max_length = min(max_length, 1000)  # 限制最大长度
                
                column_info[col] = {
                    'dtype': dtype,
                    'max_length': max_length,
                    'null_count': null_count,
                    'unique_count': unique_count,
                    'is_primary_key': is_primary_key,
                    'sample_values': sample_values
                }
            
            return {
                'filename': os.path.basename(csv_path),
                'row_count': len(df),
                'column_count': len(df.columns),
                'columns': column_info,
                'dataframe': df
            }
            
        except Exception as e:
            self.logger.error(f"分析CSV文件失败 {csv_path}: {e}")
            return None
    
    def generate_table_name(self, filename: str) -> str:
        """生成合适的表名"""
        # 移除文件扩展名
        table_name = os.path.splitext(filename)[0]
        
        # 替换特殊字符
        table_name = re.sub(r'[^a-zA-Z0-9_]', '_', table_name)
        
        # 确保以字母开头
        if not table_name[0].isalpha():
            table_name = 'table_' + table_name
        
        return table_name.lower()
    
    def generate_create_table_sql(
        self, 
        structure: Dict[str, Any], 
        db_format: str,
        table_name: str = None
    ) -> str:
        """生成CREATE TABLE SQL语句"""
        
        if not table_name:
            table_name = self.generate_table_name(structure['filename'])
        
        type_mapping = self.type_mappings.get(db_format, self.type_mappings['sqlite'])
        
        # 生成列定义
        column_definitions = []
        primary_keys = []
        used_column_names = set()

        for col_name, col_info in structure['columns'].items():
            # 清理列名
            clean_col_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
            if not clean_col_name[0].isalpha():
                clean_col_name = 'col_' + clean_col_name

            # 确保列名唯一
            original_clean_name = clean_col_name
            counter = 1
            while clean_col_name in used_column_names:
                clean_col_name = f"{original_clean_name}_{counter}"
                counter += 1
            used_column_names.add(clean_col_name)
            
            # 确定数据类型
            dtype = col_info['dtype']
            sql_type = type_mapping.get(dtype, 'TEXT')
            
            # 对于文本类型，使用实际长度
            if dtype == 'object' and db_format in ['mysql', 'postgresql', 'sql_server']:
                max_len = col_info['max_length']
                if db_format == 'mysql':
                    sql_type = f'VARCHAR({max_len})'
                elif db_format == 'postgresql':
                    sql_type = f'VARCHAR({max_len})'
                elif db_format == 'sql_server':
                    sql_type = f'NVARCHAR({max_len})'
            
            # 添加约束
            constraints = []
            if col_info['null_count'] == 0:
                constraints.append('NOT NULL')
            
            if col_info['is_primary_key']:
                primary_keys.append(clean_col_name)
            
            column_def = f'    {clean_col_name} {sql_type}'
            if constraints:
                column_def += ' ' + ' '.join(constraints)
            
            column_definitions.append(column_def)
        
        # 添加主键约束
        if primary_keys:
            column_definitions.append(f'    PRIMARY KEY ({", ".join(primary_keys)})')
        
        # 生成完整的CREATE TABLE语句
        sql = f'CREATE TABLE {table_name} (\n'
        sql += ',\n'.join(column_definitions)
        sql += '\n);'
        
        return sql
    
    def generate_insert_sql(
        self, 
        structure: Dict[str, Any], 
        table_name: str,
        batch_size: int = 1000
    ) -> List[str]:
        """生成INSERT SQL语句"""
        
        df = structure['dataframe']
        
        # 清理列名并确保唯一性
        clean_columns = []
        used_names = set()
        for col in df.columns:
            clean_col = re.sub(r'[^a-zA-Z0-9_]', '_', col)
            if not clean_col[0].isalpha():
                clean_col = 'col_' + clean_col

            # 确保列名唯一
            original_clean = clean_col
            counter = 1
            while clean_col in used_names:
                clean_col = f"{original_clean}_{counter}"
                counter += 1
            used_names.add(clean_col)
            clean_columns.append(clean_col)
        
        insert_statements = []
        
        # 分批处理数据
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            
            values_list = []
            for _, row in batch_df.iterrows():
                values = []
                for value in row:
                    if pd.isna(value):
                        values.append('NULL')
                    elif isinstance(value, str):
                        # 转义单引号
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                    else:
                        values.append(str(value))
                
                values_list.append(f"({', '.join(values)})")
            
            if values_list:
                columns_str = ', '.join(clean_columns)
                values_str = ',\n    '.join(values_list)
                
                insert_sql = f'INSERT INTO {table_name} ({columns_str})\nVALUES\n    {values_str};'
                insert_statements.append(insert_sql)
        
        return insert_statements
    
    def convert_to_sqlite(
        self, 
        csv_paths: List[str], 
        output_path: str
    ) -> Tuple[bool, str, Dict]:
        """转换为SQLite数据库"""
        try:
            # 创建SQLite数据库
            conn = sqlite3.connect(output_path)
            cursor = conn.cursor()
            
            conversion_stats = {
                'total_files': len(csv_paths),
                'successful_files': 0,
                'total_rows': 0,
                'tables': []
            }
            
            for csv_path in csv_paths:
                if not os.path.exists(csv_path):
                    continue
                
                # 分析CSV结构
                structure = self.analyze_csv_structure(csv_path)
                if not structure:
                    continue
                
                # 生成表名
                table_name = self.generate_table_name(structure['filename'])
                
                # 创建表
                create_sql = self.generate_create_table_sql(structure, 'sqlite', table_name)
                cursor.execute(create_sql)
                
                # 插入数据
                df = structure['dataframe']
                
                # 清理列名并确保唯一性
                clean_columns = []
                used_names = set()
                for col in df.columns:
                    clean_col = re.sub(r'[^a-zA-Z0-9_]', '_', col)
                    if not clean_col[0].isalpha():
                        clean_col = 'col_' + clean_col

                    # 确保列名唯一
                    original_clean = clean_col
                    counter = 1
                    while clean_col in used_names:
                        clean_col = f"{original_clean}_{counter}"
                        counter += 1
                    used_names.add(clean_col)
                    clean_columns.append(clean_col)
                
                # 重命名DataFrame列
                df.columns = clean_columns
                
                # 插入数据到SQLite
                df.to_sql(table_name, conn, if_exists='replace', index=False)
                
                conversion_stats['successful_files'] += 1
                conversion_stats['total_rows'] += len(df)
                conversion_stats['tables'].append({
                    'name': table_name,
                    'source_file': structure['filename'],
                    'rows': len(df),
                    'columns': len(df.columns)
                })
            
            conn.commit()
            conn.close()
            
            success_msg = f"✅ 成功转换 {conversion_stats['successful_files']} 个文件到SQLite数据库"
            return True, success_msg, conversion_stats
            
        except Exception as e:
            error_msg = f"❌ SQLite转换失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}
    
    def convert_to_sql_script(
        self, 
        csv_paths: List[str], 
        output_path: str,
        db_format: str = 'mysql'
    ) -> Tuple[bool, str, Dict]:
        """转换为SQL脚本文件"""
        try:
            conversion_stats = {
                'total_files': len(csv_paths),
                'successful_files': 0,
                'total_rows': 0,
                'tables': []
            }
            
            sql_content = []
            
            # 添加文件头注释
            sql_content.append(f'-- {db_format.upper()} SQL Script')
            sql_content.append(f'-- Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
            sql_content.append(f'-- Source files: {len(csv_paths)} CSV files')
            sql_content.append('')
            
            for csv_path in csv_paths:
                if not os.path.exists(csv_path):
                    continue
                
                # 分析CSV结构
                structure = self.analyze_csv_structure(csv_path)
                if not structure:
                    continue
                
                # 生成表名
                table_name = self.generate_table_name(structure['filename'])
                
                # 添加表注释
                sql_content.append(f'-- Table: {table_name}')
                sql_content.append(f'-- Source: {structure["filename"]}')
                sql_content.append(f'-- Rows: {structure["row_count"]}')
                sql_content.append('')
                
                # 生成CREATE TABLE语句
                create_sql = self.generate_create_table_sql(structure, db_format, table_name)
                sql_content.append(create_sql)
                sql_content.append('')
                
                # 生成INSERT语句
                insert_statements = self.generate_insert_sql(structure, table_name)
                sql_content.extend(insert_statements)
                sql_content.append('')
                
                conversion_stats['successful_files'] += 1
                conversion_stats['total_rows'] += structure['row_count']
                conversion_stats['tables'].append({
                    'name': table_name,
                    'source_file': structure['filename'],
                    'rows': structure['row_count'],
                    'columns': structure['column_count']
                })
            
            # 写入SQL文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(sql_content))
            
            success_msg = f"✅ 成功生成 {db_format.upper()} SQL脚本"
            return True, success_msg, conversion_stats

        except Exception as e:
            error_msg = f"❌ SQL脚本生成失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def preview_database_file(self, db_path: str, file_type: str = 'auto') -> str:
        """预览数据库文件内容"""
        try:
            if file_type == 'auto':
                # 根据文件扩展名判断类型
                ext = os.path.splitext(db_path)[1].lower()
                if ext == '.db' or ext == '.sqlite':
                    file_type = 'sqlite'
                elif ext == '.sql':
                    file_type = 'sql'
                else:
                    file_type = 'sql'

            if file_type == 'sqlite':
                return self._preview_sqlite_database(db_path)
            elif file_type == 'sql':
                return self._preview_sql_file(db_path)
            else:
                return "<p style='color: red;'>❌ 不支持的文件类型</p>"

        except Exception as e:
            return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"

    def _preview_sqlite_database(self, db_path: str) -> str:
        """预览SQLite数据库"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            if not tables:
                conn.close()
                return "<p>数据库中没有表</p>"

            html_content = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">🗄️ SQLite数据库预览</h3>
                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📊 数据库统计:</strong></p>
                    <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(db_path) / 1024:.2f} KB</p>
                    <p style="margin: 4px 0;">• 表数量: {len(tables)} 个</p>
                </div>
            """

            # 预览每个表
            for table_name, in tables:
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()

                # 获取行数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                row_count = cursor.fetchone()[0]

                # 获取前20行数据
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 20;")
                rows = cursor.fetchall()

                # 生成表预览HTML
                html_content += f"""
                <div style="margin-bottom: 20px; border: 1px solid #ccc; border-radius: 6px; overflow: hidden;">
                    <div style="background: #f8f9fa; padding: 12px; border-bottom: 1px solid #ccc;">
                        <h4 style="margin: 0; color: #333;">📋 表: {table_name}</h4>
                        <p style="margin: 4px 0 0 0; font-size: 12px; color: #666;">
                            总行数: {row_count} | 列数: {len(columns)} | 预览: {min(len(rows), 20)} 行
                        </p>
                    </div>
                    <div style="overflow: auto; max-height: 400px; background: white;">
                        <table class="db-preview-table">
                            <thead>
                                <tr>
                """

                # 添加表头
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    html_content += f"<th>{col_name}<br><small>({col_type})</small></th>"

                html_content += "</tr></thead><tbody>"

                # 添加数据行
                for row in rows:
                    html_content += "<tr>"
                    for value in row:
                        if value is None:
                            html_content += "<td style='color: #999; font-style: italic;'>NULL</td>"
                        else:
                            # 限制显示长度
                            display_value = str(value)
                            if len(display_value) > 50:
                                display_value = display_value[:47] + "..."
                            html_content += f"<td>{display_value}</td>"
                    html_content += "</tr>"

                html_content += "</tbody></table></div></div>"

            conn.close()

            # 添加样式
            html_content += """
                <style>
                .db-preview-table {
                    font-size: 11px;
                    border-collapse: collapse;
                    width: 100%;
                    margin: 0;
                }
                .db-preview-table th, .db-preview-table td {
                    border: 1px solid #ddd;
                    padding: 6px 8px;
                    text-align: left;
                    vertical-align: top;
                }
                .db-preview-table th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                    position: sticky;
                    top: 0;
                    z-index: 5;
                }
                .db-preview-table tbody tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .db-preview-table tbody tr:hover {
                    background-color: #e8f4fd;
                }
                .db-preview-table td {
                    max-width: 200px;
                    word-wrap: break-word;
                    white-space: normal;
                }
                </style>
            </div>
            """

            return html_content

        except Exception as e:
            return f"<p style='color: red;'>❌ SQLite预览失败: {str(e)}</p>"

    def _preview_sql_file(self, sql_path: str) -> str:
        """预览SQL文件"""
        try:
            with open(sql_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 分析SQL内容
            lines = content.split('\n')
            total_lines = len(lines)

            # 统计CREATE TABLE和INSERT语句
            create_tables = len([line for line in lines if line.strip().upper().startswith('CREATE TABLE')])
            insert_statements = len([line for line in lines if line.strip().upper().startswith('INSERT INTO')])

            # 获取前100行用于预览
            preview_lines = lines[:100]
            preview_content = '\n'.join(preview_lines)

            # 如果内容被截断，添加提示
            if total_lines > 100:
                preview_content += f"\n\n-- ... 还有 {total_lines - 100} 行内容 ..."

            html_content = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">📄 SQL文件预览</h3>
                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📊 文件统计:</strong></p>
                    <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(sql_path) / 1024:.2f} KB</p>
                    <p style="margin: 4px 0;">• 总行数: {total_lines} 行</p>
                    <p style="margin: 4px 0;">• CREATE TABLE语句: {create_tables} 个</p>
                    <p style="margin: 4px 0;">• INSERT语句: {insert_statements} 个</p>
                </div>

                <h4 style="color: #333; margin-bottom: 12px;">📋 SQL内容预览:</h4>
                <div style="overflow: auto; max-height: 500px; border: 1px solid #ccc; border-radius: 4px; background: #f8f8f8; padding: 12px;">
                    <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.4; white-space: pre-wrap;">{preview_content}</pre>
                </div>

                <div style="margin-top: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
                    💡 提示: 显示前100行内容，完整内容包含 {total_lines} 行SQL语句
                </div>
            </div>
            """

            return html_content

        except Exception as e:
            return f"<p style='color: red;'>❌ SQL文件预览失败: {str(e)}</p>"
