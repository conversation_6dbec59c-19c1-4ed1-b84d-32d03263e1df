#!/usr/bin/env python3
"""
价格计算器
负责管理单价数据库和执行价格计算
"""

import os
import json
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime

class PriceCalculator:
    """价格计算器类"""
    
    def __init__(self):
        self.price_database_path = "data/price_database.json"
        self.price_database = self._load_price_database()
    
    def _load_price_database(self) -> Dict[str, Any]:
        """加载单价数据库"""
        try:
            if os.path.exists(self.price_database_path):
                with open(self.price_database_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认单价数据库
                return self._create_default_price_database()
        except Exception as e:
            print(f"加载单价数据库失败: {e}")
            return self._create_default_price_database()
    
    def _create_default_price_database(self) -> Dict[str, Any]:
        """创建默认单价数据库"""
        default_prices = {
            "metadata": {
                "version": "1.0",
                "last_updated": datetime.now().isoformat(),
                "description": "北京定额单价数据库"
            },
            "categories": {
                "人工": {
                    "description": "人工费用",
                    "default_unit": "工日",
                    "resources": {
                        "00010701": {
                            "name": "综合用工三类",
                            "unit": "工日",
                            "price": 120.00,
                            "description": "综合用工三类工日单价"
                        },
                        "00010702": {
                            "name": "综合用工二类",
                            "unit": "工日",
                            "price": 130.00,
                            "description": "综合用工二类工日单价"
                        },
                        "00010703": {
                            "name": "综合用工一类",
                            "unit": "工日",
                            "price": 140.00,
                            "description": "综合用工一类工日单价"
                        }
                    }
                },
                "机械": {
                    "description": "机械费用",
                    "default_unit": "台班",
                    "resources": {
                        "99030030": {
                            "name": "电动打钎机",
                            "unit": "台班",
                            "price": 50.00,
                            "description": "电动打钎机台班单价"
                        },
                        "99030031": {
                            "name": "挖掘机",
                            "unit": "台班",
                            "price": 800.00,
                            "description": "挖掘机台班单价"
                        },
                        "99030032": {
                            "name": "推土机",
                            "unit": "台班",
                            "price": 600.00,
                            "description": "推土机台班单价"
                        }
                    }
                },
                "材料": {
                    "description": "材料费用",
                    "default_unit": "t",
                    "resources": {
                        "01010001": {
                            "name": "水泥",
                            "unit": "t",
                            "price": 450.00,
                            "description": "普通硅酸盐水泥"
                        },
                        "01010002": {
                            "name": "砂",
                            "unit": "m³",
                            "price": 80.00,
                            "description": "中砂"
                        },
                        "01010003": {
                            "name": "石子",
                            "unit": "m³",
                            "price": 90.00,
                            "description": "碎石"
                        }
                    }
                },
                "其他费用": {
                    "description": "其他费用",
                    "default_unit": "%",
                    "resources": {
                        "99460004": {
                            "name": "其他机具费占人工费",
                            "unit": "%",
                            "price": 0.00,  # 百分比项的单价会自动计算
                            "description": "其他机具费占人工费的百分比"
                        }
                    }
                }
            }
        }
        
        # 保存默认数据库
        self._save_price_database(default_prices)
        return default_prices
    
    def _save_price_database(self, database: Dict[str, Any]):
        """保存单价数据库"""
        try:
            os.makedirs(os.path.dirname(self.price_database_path), exist_ok=True)
            with open(self.price_database_path, 'w', encoding='utf-8') as f:
                json.dump(database, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存单价数据库失败: {e}")
    
    def get_unit_price(self, resource_code: str, category: str = None) -> float:
        """获取资源单价"""
        try:
            # 首先在指定类别中查找
            if category and category in self.price_database.get("categories", {}):
                resources = self.price_database["categories"][category].get("resources", {})
                if resource_code in resources:
                    return float(resources[resource_code]["price"])
            
            # 在所有类别中查找
            for cat_name, cat_data in self.price_database.get("categories", {}).items():
                resources = cat_data.get("resources", {})
                if resource_code in resources:
                    return float(resources[resource_code]["price"])
            
            # 如果找不到，返回默认值
            print(f"未找到资源编号 {resource_code} 的单价，使用默认值 0.00")
            return 0.00
            
        except Exception as e:
            print(f"获取单价失败: {e}")
            return 0.00
    
    def update_unit_price(self, resource_code: str, price: float, category: str = None):
        """更新资源单价"""
        try:
            updated = False
            
            # 首先在指定类别中查找并更新
            if category and category in self.price_database.get("categories", {}):
                resources = self.price_database["categories"][category].get("resources", {})
                if resource_code in resources:
                    resources[resource_code]["price"] = price
                    updated = True
            
            # 如果没有指定类别或在指定类别中没找到，在所有类别中查找
            if not updated:
                for cat_name, cat_data in self.price_database.get("categories", {}).items():
                    resources = cat_data.get("resources", {})
                    if resource_code in resources:
                        resources[resource_code]["price"] = price
                        updated = True
                        break
            
            if updated:
                # 更新时间戳
                self.price_database["metadata"]["last_updated"] = datetime.now().isoformat()
                self._save_price_database(self.price_database)
                print(f"已更新资源编号 {resource_code} 的单价为 {price}")
            else:
                print(f"未找到资源编号 {resource_code}，无法更新单价")
                
        except Exception as e:
            print(f"更新单价失败: {e}")
    
    def calculate_prices(self, parent_data: List[Dict], child_data: List[Dict]):
        """计算价格（增强版）"""
        try:
            # 首先为所有子项设置单价
            for child in child_data:
                resource_code = child.get("resource_code", "")
                category = child.get("category", "")
                
                # 如果单价为0，尝试从数据库获取
                if child.get("unit_price", 0) == 0:
                    unit_price = self.get_unit_price(resource_code, category)
                    child["unit_price"] = unit_price
            
            # 按定额编号分组
            quota_groups = {}
            for item in parent_data:
                quota_code = item["quota_code"]
                if quota_code not in quota_groups:
                    quota_groups[quota_code] = {"parent": item, "children": []}
            
            for item in child_data:
                quota_code = item["quota_code"]
                if quota_code in quota_groups:
                    quota_groups[quota_code]["children"].append(item)
            
            # 计算每个定额组的价格
            for quota_code, group in quota_groups.items():
                parent = group["parent"]
                children = group["children"]
                
                total_price = 0
                percent_items = []
                base_price = 0  # 用于百分比计算的基数
                
                # 第一轮：计算非百分比项的合价
                for child in children:
                    consumption = float(child.get("consumption", 0))
                    unit_price = float(child.get("unit_price", 0))
                    resource_unit = str(child.get("resource_unit", ""))
                    
                    if "%" in resource_unit:
                        # 百分比项，稍后处理
                        percent_items.append(child)
                    else:
                        # 普通项：合价 = 单价 * 消耗量
                        child["total_price"] = unit_price * consumption
                        total_price += child["total_price"]
                        base_price += child["total_price"]
                
                # 第二轮：处理百分比项
                for child in percent_items:
                    consumption = float(child.get("consumption", 0))
                    # 百分比项的单价设置为基数（用于显示）
                    child["unit_price"] = base_price
                    # 合价 = 基数 * 消耗量 / 100 (百分比转换)
                    child["total_price"] = base_price * consumption / 100
                    total_price += child["total_price"]
                
                # 设置父级综合单价
                parent["comprehensive_price"] = total_price
                
            print(f"价格计算完成，处理了 {len(quota_groups)} 个定额组")
            
        except Exception as e:
            print(f"计算价格失败: {e}")
    
    def get_price_summary(self, child_data: List[Dict]) -> Dict[str, Any]:
        """获取价格汇总信息"""
        try:
            summary = {
                "total_items": len(child_data),
                "categories": {},
                "total_cost": 0,
                "items_with_price": 0,
                "items_without_price": 0
            }
            
            for child in child_data:
                category = child.get("category", "未分类")
                unit_price = float(child.get("unit_price", 0))
                total_price = float(child.get("total_price", 0))
                
                if category not in summary["categories"]:
                    summary["categories"][category] = {
                        "count": 0,
                        "total_cost": 0,
                        "items": []
                    }
                
                summary["categories"][category]["count"] += 1
                summary["categories"][category]["total_cost"] += total_price
                summary["categories"][category]["items"].append({
                    "resource_code": child.get("resource_code", ""),
                    "resource_name": child.get("resource_name", ""),
                    "unit_price": unit_price,
                    "total_price": total_price
                })
                
                summary["total_cost"] += total_price
                
                if unit_price > 0:
                    summary["items_with_price"] += 1
                else:
                    summary["items_without_price"] += 1
            
            return summary
            
        except Exception as e:
            print(f"生成价格汇总失败: {e}")
            return {}
    
    def export_price_database_csv(self) -> str:
        """导出单价数据库为CSV文件"""
        try:
            export_data = []
            
            for category, cat_data in self.price_database.get("categories", {}).items():
                for resource_code, resource_info in cat_data.get("resources", {}).items():
                    export_data.append({
                        "类别": category,
                        "资源编号": resource_code,
                        "资源名称": resource_info["name"],
                        "单位": resource_info["unit"],
                        "单价": resource_info["price"],
                        "说明": resource_info.get("description", "")
                    })
            
            df = pd.DataFrame(export_data)
            
            # 确保输出目录存在
            os.makedirs("output", exist_ok=True)
            
            filename = f"price_database_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            file_path = os.path.join("output", filename)
            
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            print(f"单价数据库已导出到: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"导出单价数据库失败: {e}")
            return ""
