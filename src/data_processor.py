#!/usr/bin/env python3
"""
数据处理模块
负责解析AI识别结果并生成CSV文件
"""

import json
import re
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
import pandas as pd

from .config import Config

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.config = Config()
    
    def parse_recognition_result(self, response_text: str, page_number: int) -> List[Dict[str, Any]]:
        """
        解析AI识别结果
        
        Args:
            response_text: AI响应文本
            page_number: 页码
            
        Returns:
            List[Dict[str, Any]]: 解析后的定额数据列表
        """
        try:
            if not response_text:
                return []
            
            # 尝试提取JSON内容
            json_data = self._extract_json_from_text(response_text)
            
            if json_data:
                return self._process_json_data(json_data, page_number)
            else:
                # 如果没有JSON，尝试文本解析
                return self._process_text_data(response_text, page_number)
                
        except Exception as e:
            print(f"解析识别结果失败: {str(e)}")
            return []
    
    def _extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """从文本中提取JSON数据"""
        try:
            # 查找JSON代码块
            json_pattern = r'```json\s*(.*?)\s*```'
            json_match = re.search(json_pattern, text, re.DOTALL | re.IGNORECASE)
            
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            
            # 查找裸JSON
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, text, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            
            return None
            
        except json.JSONDecodeError:
            return None
    
    def _process_json_data(self, json_data: Dict[str, Any], page_number: int) -> List[Dict[str, Any]]:
        """处理JSON格式的数据"""
        processed_data = []

        try:
            # 优先处理新格式：包含quotas数组的多定额项格式
            if "quotas" in json_data and isinstance(json_data["quotas"], list):
                print(f"检测到新格式，包含 {len(json_data['quotas'])} 个定额项")
                for i, quota in enumerate(json_data["quotas"]):
                    if "parent_quota" in quota and "resource_consumption" in quota:
                        quota_data = self._process_single_quota(quota, page_number)
                        if quota_data:
                            processed_data.extend(quota_data)
                            print(f"成功处理第 {i+1} 个定额项: {quota['parent_quota'].get('code', 'Unknown')}")

            # 处理旧格式：单个定额项
            elif "parent_quota" in json_data and "resource_consumption" in json_data:
                print("检测到旧格式，单个定额项")
                quota_data = self._process_single_quota(json_data, page_number)
                if quota_data:
                    processed_data.extend(quota_data)

            # 处理数组格式
            elif isinstance(json_data, list):
                print(f"检测到数组格式，包含 {len(json_data)} 个元素")
                for i, item in enumerate(json_data):
                    if "parent_quota" in item and "resource_consumption" in item:
                        quota_data = self._process_single_quota(item, page_number)
                        if quota_data:
                            processed_data.extend(quota_data)
                            print(f"成功处理数组中第 {i+1} 个定额项")

            else:
                print(f"未识别的JSON格式，键: {list(json_data.keys()) if isinstance(json_data, dict) else type(json_data)}")

        except Exception as e:
            print(f"处理JSON数据失败: {str(e)}")

        print(f"总共处理了 {len(processed_data)} 条数据记录")
        return processed_data
    
    def _process_single_quota(self, quota_data: Dict[str, Any], page_number: int) -> List[Dict[str, Any]]:
        """处理单个定额项数据"""
        try:
            parent_quota = quota_data.get("parent_quota", {})
            resource_consumption = quota_data.get("resource_consumption", [])
            
            if not parent_quota.get("code"):
                return []
            
            processed_items = []
            
            # 处理父级定额信息
            parent_item = {
                "type": "parent",
                "page_number": page_number,
                "quota_code": parent_quota.get("code", ""),
                "quota_name": parent_quota.get("name", ""),
                "work_content": parent_quota.get("work_content", ""),
                "unit": parent_quota.get("unit", ""),
                "comprehensive_price": 0,  # 初始值为0，后续计算
                "resource_code": "",
                "category": "",
                "resource_name": "",
                "resource_unit": "",
                "consumption": 0,
                "unit_price": 0,
                "total_price": 0
            }
            processed_items.append(parent_item)
            
            # 处理子级资源消耗信息
            for resource in resource_consumption:
                child_item = {
                    "type": "child",
                    "page_number": page_number,
                    "quota_code": parent_quota.get("code", ""),
                    "quota_name": "",
                    "work_content": "",
                    "unit": "",
                    "comprehensive_price": 0,
                    "resource_code": resource.get("resource_code", ""),
                    "category": resource.get("category", ""),
                    "resource_name": resource.get("name", ""),
                    "resource_unit": resource.get("unit", ""),
                    "consumption": self._parse_number(resource.get("consumption", "")),
                    "unit_price": 0,  # 初始值为0，用户输入
                    "total_price": 0   # 初始值为0，后续计算
                }
                processed_items.append(child_item)
            
            return processed_items
            
        except Exception as e:
            print(f"处理单个定额项失败: {str(e)}")
            return []
    
    def _process_text_data(self, text: str, page_number: int) -> List[Dict[str, Any]]:
        """处理纯文本格式的数据"""
        try:
            processed_data = []
            
            # 使用正则表达式提取定额信息
            # 这里需要根据实际的文本格式来调整正则表达式
            
            # 提取定额编号和名称
            quota_pattern = r'定额编号[：:]\s*([^\s]+).*?定额名称[：:]\s*([^\n]+)'
            quota_matches = re.findall(quota_pattern, text, re.IGNORECASE)
            
            for quota_code, quota_name in quota_matches:
                # 创建父级定额项
                parent_item = {
                    "type": "parent",
                    "page_number": page_number,
                    "quota_code": quota_code.strip(),
                    "quota_name": quota_name.strip(),
                    "work_content": "",
                    "unit": "",
                    "comprehensive_price": 0,
                    "resource_code": "",
                    "category": "",
                    "resource_name": "",
                    "consumption": "",
                    "unit_price": "",
                    "total_price": ""
                }
                processed_data.append(parent_item)
            
            # 提取资源消耗信息
            resource_pattern = r'资源编号[：:]\s*([^\s]+).*?资源名称[：:]\s*([^\n]+).*?消耗量[：:]\s*([^\s]+)'
            resource_matches = re.findall(resource_pattern, text, re.IGNORECASE)
            
            for resource_code, resource_name, consumption in resource_matches:
                child_item = {
                    "type": "child",
                    "page_number": page_number,
                    "quota_code": "",  # 需要关联到对应的定额编号
                    "quota_name": "",
                    "work_content": "",
                    "unit": "",
                    "comprehensive_price": "",
                    "resource_code": resource_code.strip(),
                    "category": "",
                    "resource_name": resource_name.strip(),
                    "consumption": self._parse_number(consumption),
                    "unit_price": 0,
                    "total_price": 0
                }
                processed_data.append(child_item)
            
            return processed_data
            
        except Exception as e:
            print(f"处理文本数据失败: {str(e)}")
            return []
    
    def _parse_number(self, value: str) -> float:
        """解析数字字符串"""
        try:
            if not value:
                return 0.0
            
            # 移除非数字字符（保留小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', str(value))
            
            if cleaned:
                return float(cleaned)
            else:
                return 0.0
                
        except (ValueError, TypeError):
            return 0.0
    
    def generate_csv(self, processed_data: List[Dict[str, Any]]) -> str:
        """
        生成CSV文件
        
        Args:
            processed_data: 处理后的数据列表
            
        Returns:
            str: CSV文件路径
        """
        try:
            if not processed_data:
                raise Exception("没有数据可以生成CSV文件")
            
            # 分离父级和子级数据
            parent_data = [item for item in processed_data if item["type"] == "parent"]
            child_data = [item for item in processed_data if item["type"] == "child"]
            
            # 计算价格
            self._calculate_prices(parent_data, child_data)
            
            # 生成父级CSV
            parent_csv_path = self._generate_parent_csv(parent_data)
            
            # 生成子级CSV
            child_csv_path = self._generate_child_csv(child_data)
            
            # 生成合并CSV
            combined_csv_path = self._generate_combined_csv(parent_data, child_data)
            
            return str(combined_csv_path)
            
        except Exception as e:
            raise Exception(f"生成CSV文件失败: {str(e)}")
    
    def _calculate_prices(self, parent_data: List[Dict], child_data: List[Dict]):
        """计算价格"""
        try:
            # 按定额编号分组
            quota_groups = {}
            for item in parent_data:
                quota_code = item["quota_code"]
                if quota_code not in quota_groups:
                    quota_groups[quota_code] = {"parent": item, "children": []}
            
            for item in child_data:
                quota_code = item["quota_code"]
                if quota_code in quota_groups:
                    quota_groups[quota_code]["children"].append(item)
            
            # 计算每个定额组的价格
            for quota_code, group in quota_groups.items():
                parent = group["parent"]
                children = group["children"]
                
                total_price = 0
                percent_items = []
                
                # 计算非百分比项的合价
                for child in children:
                    consumption = child["consumption"]
                    unit_price = child["unit_price"]
                    resource_unit = child.get("resource_unit", "")

                    if "%" in str(resource_unit):
                        # 百分比项，稍后处理
                        percent_items.append(child)
                    else:
                        # 普通项：合价 = 单价 * 消耗量
                        child["total_price"] = unit_price * consumption
                        total_price += child["total_price"]

                # 处理百分比项
                for child in percent_items:
                    consumption = child["consumption"]
                    # 百分比项的单价为其他项合价总和
                    child["unit_price"] = total_price
                    # 合价 = 单价 * 消耗量 / 100 (百分比转换)
                    child["total_price"] = total_price * consumption / 100
                    total_price += child["total_price"]
                
                # 设置父级综合单价
                parent["comprehensive_price"] = total_price
                
        except Exception as e:
            print(f"计算价格失败: {str(e)}")
    
    def _generate_parent_csv(self, parent_data: List[Dict]) -> str:
        """生成父级CSV文件"""
        df = pd.DataFrame([
            {
                "编号": item["quota_code"],
                "定额项名称": item["quota_name"],
                "工作内容": item["work_content"],
                "单位": item["unit"],
                "综合单价（元/单位）": item["comprehensive_price"]
            }
            for item in parent_data
        ])
        
        filename = f"parent_quotas_{uuid.uuid4().hex[:8]}.csv"
        file_path = self.config.get_output_file_path(filename)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        
        return str(file_path)
    
    def _generate_child_csv(self, child_data: List[Dict]) -> str:
        """生成子级CSV文件"""
        df = pd.DataFrame([
            {
                "定额编号": item["quota_code"],
                "资源编号": item["resource_code"],
                "类别": item["category"],
                "子项名称": item["resource_name"],
                "单位": item["resource_unit"],
                "消耗量": item["consumption"],
                "单价": item["unit_price"],
                "合价": item["total_price"]
            }
            for item in child_data
        ])
        
        filename = f"child_resources_{uuid.uuid4().hex[:8]}.csv"
        file_path = self.config.get_output_file_path(filename)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        
        return str(file_path)
    
    def _generate_combined_csv(self, parent_data: List[Dict], child_data: List[Dict]) -> str:
        """生成合并CSV文件，分别显示定额项和资源消耗"""
        combined_data = []

        # 按定额编号分组
        quota_groups = {}
        for item in parent_data:
            quota_code = item["quota_code"]
            quota_groups[quota_code] = {"parent": item, "children": []}

        for item in child_data:
            quota_code = item["quota_code"]
            if quota_code in quota_groups:
                quota_groups[quota_code]["children"].append(item)

        # 添加定额项表头
        combined_data.append({
            "类型": "📋 定额项信息",
            "编号": "定额编号",
            "名称": "定额项名称",
            "工作内容": "工作内容",
            "单位": "单位",
            "综合单价": "综合单价（元/单位）",
            "资源编号": "",
            "类别": "",
            "资源名称": "",
            "资源单位": "",
            "消耗量": "",
            "单价": "",
            "合价": ""
        })

        # 添加定额项数据
        for quota_code, group in quota_groups.items():
            parent = group["parent"]
            combined_data.append({
                "类型": "定额项",
                "编号": parent["quota_code"],
                "名称": parent["quota_name"],
                "工作内容": parent["work_content"],
                "单位": parent["unit"],
                "综合单价": parent["comprehensive_price"],
                "资源编号": "",
                "类别": "",
                "资源名称": "",
                "资源单位": "",
                "消耗量": "",
                "单价": "",
                "合价": ""
            })

        # 添加空行分隔
        combined_data.append({
            "类型": "",
            "编号": "",
            "名称": "",
            "工作内容": "",
            "单位": "",
            "综合单价": "",
            "资源编号": "",
            "类别": "",
            "资源名称": "",
            "资源单位": "",
            "消耗量": "",
            "单价": "",
            "合价": ""
        })

        # 添加资源消耗表头
        combined_data.append({
            "类型": "🔧 资源消耗信息",
            "编号": "定额编号",
            "名称": "",
            "工作内容": "",
            "单位": "",
            "综合单价": "",
            "资源编号": "资源编号",
            "类别": "类别",
            "资源名称": "资源名称",
            "资源单位": "资源单位",
            "消耗量": "消耗量",
            "单价": "单价",
            "合价": "合价"
        })

        # 添加资源消耗数据
        for quota_code, group in quota_groups.items():
            children = group["children"]
            for child in children:
                combined_data.append({
                    "类型": "资源消耗",
                    "编号": child["quota_code"],
                    "名称": "",
                    "工作内容": "",
                    "单位": "",
                    "综合单价": "",
                    "资源编号": child["resource_code"],
                    "类别": child["category"],
                    "资源名称": child["resource_name"],
                    "资源单位": child["resource_unit"],
                    "消耗量": child["consumption"],
                    "单价": child["unit_price"],
                    "合价": child["total_price"]
                })

        df = pd.DataFrame(combined_data)

        filename = f"quota_extraction_result_{uuid.uuid4().hex[:8]}.csv"
        file_path = self.config.get_output_file_path(filename)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')

        return str(file_path)
