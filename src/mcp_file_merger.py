#!/usr/bin/env python3
"""
MCP文件合并工具
Multi-CSV Processing (MCP) Tool for merging multiple quota extraction results
"""

import os
import pandas as pd
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging

class MCPFileMerger:
    """MCP文件合并工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def merge_csv_files(
        self, 
        file_paths: List[str], 
        output_path: str, 
        merge_mode: str = "by_page"
    ) -> Tuple[bool, str, Dict]:
        """
        合并多个CSV文件
        
        Args:
            file_paths: CSV文件路径列表
            output_path: 输出文件路径
            merge_mode: 合并模式 ('by_page', 'by_filename', 'by_time')
            
        Returns:
            (成功标志, 状态消息, 统计信息)
        """
        try:
            if not file_paths:
                return False, "❌ 没有选择要合并的文件", {}
            
            # 读取所有CSV文件
            dataframes = []
            file_stats = []
            
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    self.logger.warning(f"文件不存在: {file_path}")
                    continue
                
                try:
                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                    
                    # 添加文件来源信息
                    filename = os.path.basename(file_path)
                    df['文件来源'] = filename
                    
                    # 提取页码信息（如果文件名包含页码）
                    page_match = re.search(r'page[_\-]?(\d+)', filename, re.IGNORECASE)
                    if page_match:
                        df['源页码'] = int(page_match.group(1))
                    else:
                        df['源页码'] = 0
                    
                    # 添加文件修改时间
                    mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    df['文件时间'] = mod_time
                    
                    dataframes.append(df)
                    
                    # 统计信息
                    file_stats.append({
                        'filename': filename,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'size_kb': os.path.getsize(file_path) / 1024,
                        'mod_time': mod_time
                    })
                    
                except Exception as e:
                    self.logger.error(f"读取文件失败 {file_path}: {e}")
                    continue
            
            if not dataframes:
                return False, "❌ 没有成功读取任何文件", {}
            
            # 合并数据框
            merged_df = pd.concat(dataframes, ignore_index=True)
            
            # 根据合并模式排序
            if merge_mode == "by_page":
                # 按页码排序
                merged_df = merged_df.sort_values(['源页码', '文件来源'], ascending=[True, True])
            elif merge_mode == "by_filename":
                # 按文件名排序
                merged_df = merged_df.sort_values(['文件来源'], ascending=[True])
            elif merge_mode == "by_time":
                # 按文件时间排序
                merged_df = merged_df.sort_values(['文件时间', '源页码'], ascending=[True, True])
            
            # 重置索引
            merged_df = merged_df.reset_index(drop=True)
            
            # 移除临时列（保留文件来源）
            if '文件时间' in merged_df.columns:
                merged_df = merged_df.drop('文件时间', axis=1)
            
            # 保存合并后的文件
            merged_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            # 生成统计信息
            stats = {
                'total_files': len(file_stats),
                'total_rows': len(merged_df),
                'total_columns': len(merged_df.columns),
                'output_size_kb': os.path.getsize(output_path) / 1024,
                'file_stats': file_stats,
                'merge_mode': merge_mode
            }
            
            success_msg = f"✅ 成功合并 {stats['total_files']} 个文件，共 {stats['total_rows']} 行数据"
            
            return True, success_msg, stats
            
        except Exception as e:
            error_msg = f"❌ 合并失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}
    
    def analyze_files_compatibility(self, file_paths: List[str]) -> Dict:
        """
        分析文件兼容性
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            兼容性分析结果
        """
        try:
            if not file_paths:
                return {'compatible': False, 'message': '没有文件可分析'}
            
            # 读取所有文件的列信息
            all_columns = []
            file_info = []
            
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    continue
                
                try:
                    df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=0)  # 只读取列名
                    columns = list(df.columns)
                    all_columns.append(set(columns))
                    
                    file_info.append({
                        'filename': os.path.basename(file_path),
                        'columns': columns,
                        'column_count': len(columns)
                    })
                    
                except Exception as e:
                    self.logger.warning(f"分析文件失败 {file_path}: {e}")
                    continue
            
            if not all_columns:
                return {'compatible': False, 'message': '没有成功读取任何文件'}
            
            # 检查列兼容性
            common_columns = set.intersection(*all_columns) if all_columns else set()
            all_unique_columns = set.union(*all_columns) if all_columns else set()
            
            # 判断兼容性
            compatible = len(common_columns) > 0
            
            analysis = {
                'compatible': compatible,
                'common_columns': list(common_columns),
                'all_columns': list(all_unique_columns),
                'common_column_count': len(common_columns),
                'total_unique_columns': len(all_unique_columns),
                'file_info': file_info,
                'message': f"{'✅ 文件兼容' if compatible else '⚠️ 文件结构不完全一致'}"
            }
            
            return analysis
            
        except Exception as e:
            return {
                'compatible': False, 
                'message': f'分析失败: {str(e)}'
            }
    
    def generate_merge_preview(
        self,
        file_paths: List[str],
        merge_mode: str = "by_page",
        preview_rows: int = 20
    ) -> str:
        """
        生成合并预览HTML

        Args:
            file_paths: 文件路径列表
            merge_mode: 合并模式
            preview_rows: 预览行数

        Returns:
            预览HTML
        """
        try:
            # 分析文件兼容性
            compatibility = self.analyze_files_compatibility(file_paths)

            # 读取更多数据进行预览
            preview_data = []
            total_rows = 0

            for file_path in file_paths[:5]:  # 最多预览5个文件
                if not os.path.exists(file_path):
                    continue

                try:
                    # 先读取完整文件信息
                    df_full = pd.read_csv(file_path, encoding='utf-8-sig')
                    df_preview = df_full.head(preview_rows)
                    filename = os.path.basename(file_path)

                    total_rows += len(df_full)

                    preview_data.append({
                        'filename': filename,
                        'total_rows': len(df_full),
                        'preview_rows': len(df_preview),
                        'columns': list(df_full.columns),
                        'sample_data': df_preview.to_html(index=True, classes='preview-table', escape=False)
                    })

                except Exception as e:
                    continue
            
            # 生成预览HTML
            html = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">🔗 文件合并预览</h3>

                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📊 合并统计:</strong></p>
                    <p style="margin: 4px 0;">• 合并模式: {merge_mode}</p>
                    <p style="margin: 4px 0;">• 文件数量: {len(file_paths)} 个</p>
                    <p style="margin: 4px 0;">• 预计总行数: {total_rows} 行</p>
                    <p style="margin: 4px 0;">• 兼容性: {compatibility['message']}</p>
                    <p style="margin: 4px 0;">• 共同列数: {compatibility.get('common_column_count', 0)} 列</p>
                </div>

                <h4 style="color: #333;">📋 文件详细预览:</h4>
            """

            for i, data in enumerate(preview_data):
                html += f"""
                <div style="margin-bottom: 20px; border: 1px solid #ccc; border-radius: 6px; overflow: hidden;">
                    <div style="background: #f8f9fa; padding: 12px; border-bottom: 1px solid #ccc;">
                        <h5 style="margin: 0; color: #333;">📄 {data['filename']}</h5>
                        <p style="margin: 4px 0 0 0; font-size: 12px; color: #666;">
                            总行数: {data['total_rows']} | 预览: {data['preview_rows']} 行 | 列数: {len(data['columns'])}
                        </p>
                        <p style="margin: 4px 0 0 0; font-size: 11px; color: #888;">
                            列名: {', '.join(data['columns'][:5])}{'...' if len(data['columns']) > 5 else ''}
                        </p>
                    </div>
                    <div style="overflow: auto; max-height: 300px; background: white;">
                        {data['sample_data']}
                    </div>
                </div>
                """
            
            html += f"""
                <div style="margin-top: 16px; padding: 12px; background: #f0f8ff; border-radius: 6px; font-size: 12px; color: #666;">
                    💡 预览说明: 显示每个文件的前 {preview_rows} 行数据，合并后将包含所有 {total_rows} 行数据
                </div>

                <style>
                .preview-table {{
                    font-size: 10px;
                    border-collapse: collapse;
                    width: 100%;
                    margin: 0;
                }}
                .preview-table th, .preview-table td {{
                    border: 1px solid #ddd;
                    padding: 4px 6px;
                    text-align: left;
                    vertical-align: top;
                }}
                .preview-table th {{
                    background-color: #f8f9fa;
                    font-weight: bold;
                    position: sticky;
                    top: 0;
                    z-index: 5;
                }}
                .preview-table tbody tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                .preview-table tbody tr:hover {{
                    background-color: #e8f4fd;
                }}
                .preview-table td {{
                    max-width: 150px;
                    word-wrap: break-word;
                    white-space: normal;
                }}
                </style>
            </div>
            """
            
            return html
            
        except Exception as e:
            return f"<p style='color: red;'>❌ 生成预览失败: {str(e)}</p>"
