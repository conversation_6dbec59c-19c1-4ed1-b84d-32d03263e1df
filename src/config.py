#!/usr/bin/env python3
"""
配置文件
"""

import os
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """系统配置类"""
    
    def __init__(self):
        # 基础路径
        self.BASE_DIR = Path(__file__).parent.parent
        self.TEMP_DIR = self.BASE_DIR / "temp"
        self.OUTPUT_DIR = self.BASE_DIR / "output"
        
        # 创建必要目录
        self.TEMP_DIR.mkdir(exist_ok=True)
        self.OUTPUT_DIR.mkdir(exist_ok=True)
        
        # DeepSeek配置
        self.DEEPSEEK_URL = "https://chat.deepseek.com/"
        self.DEEPSEEK_LOGIN_URL = "https://chat.deepseek.com/sign_in"
        
        # 浏览器配置
        self.BROWSER_HEADLESS = os.getenv("BROWSER_HEADLESS", "false").lower() == "true"
        self.BROWSER_TIMEOUT = int(os.getenv("BROWSER_TIMEOUT", "30"))
        
        # PDF处理配置
        self.PDF_DPI = int(os.getenv("PDF_DPI", "200"))
        self.IMAGE_FORMAT = os.getenv("IMAGE_FORMAT", "PNG")
        
        # 定额信息提取配置
        self.QUOTA_EXTRACTION_PROMPT = """
        请仔细分析这张北京市消耗定额表格图片，按照以下格式提取信息：

        1. **识别表格结构**：
           - 确定表格上半部分的定额编号列（如：1-11、1-12、1-13）
           - 识别每个定额项对应的名称和工作内容
           - 确认表格下半部分的资源消耗明细

        2. **提取每个定额项的信息**：
           - 定额编号（如：1-1、1-2、1-3）
           - 定额项名称：需要组合主项名称和差异描述
             * 主项名称（如：人工挖一般土方）
             * 差异描述（如：一、二类土、三类土、四类土）
             * 完整名称（如：人工挖一般土方 一、二类土）
           - 工作内容（如：挖土、余土清理、修整底边等）
           - 单位（如：m³）

        3. **提取资源消耗信息**（每个定额项都有相同的资源消耗）：
           - 资源编号（如：00010701）
           - 类别（如：人工、机械、其他费用）
           - 子项名称（如：综合用工三类、电动打钉机、其他机具费占人工费）
           - 单位（如：工日、台班、%）
           - 消耗量（如：0.200、0.0039、1.50）

        **重要说明**：
        - 图片中可能有多个定额项（如1-1、1-2、1-3），每个定额项都要单独提取
        - 定额项名称要完整：主项名称 + 差异描述（如：人工挖一般土方 一、二类土）
        - 仔细观察表格中每列的差异描述文字，将其合并到定额项名称中
        - 每个定额项通常有相同的资源消耗明细，但消耗量可能不同
        - 请特别注意单位为"%"的资源项，这些是特殊的百分比费用项
        - 确保提取所有资源消耗行，包括人工、材料、机械、其他费用等
        - 消耗量请保留小数点后的精度

        请以JSON格式返回结果（支持多个定额项）：
        {
            "quotas": [
                {
                    "parent_quota": {
                        "code": "定额编号",
                        "name": "定额项名称",
                        "work_content": "工作内容",
                        "unit": "单位"
                    },
                    "resource_consumption": [
                        {
                            "resource_code": "资源编号",
                            "category": "类别",
                            "name": "子项名称",
                            "unit": "单位",
                            "consumption": "消耗量"
                        }
                    ]
                }
            ]
        }

        如果只有一个定额项，quotas数组中只包含一个元素。
        如果有多个定额项（如1-11、1-12、1-13），quotas数组中包含多个元素。
        """
        
        # CSV输出配置
        self.CSV_COLUMNS_PARENT = [
            "定额编号", "定额名称", "工作内容", "单位", "综合单价"
        ]
        
        self.CSV_COLUMNS_CHILD = [
            "定额编号", "资源编号", "资源类别", "子项名称", "单位", "消耗量", "单价", "合价"
        ]
        
        # MCP服务器配置
        self.MCP_PDF_SERVER_PORT = int(os.getenv("MCP_PDF_SERVER_PORT", "8001"))
        self.MCP_BROWSER_SERVER_PORT = int(os.getenv("MCP_BROWSER_SERVER_PORT", "8002"))
        
    def get_temp_file_path(self, filename: str) -> Path:
        """获取临时文件路径"""
        return self.TEMP_DIR / filename
    
    def get_output_file_path(self, filename: str) -> Path:
        """获取输出文件路径"""
        return self.OUTPUT_DIR / filename
