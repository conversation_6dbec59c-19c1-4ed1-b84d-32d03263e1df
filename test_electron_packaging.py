#!/usr/bin/env python3
"""
测试Electron打包系统
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def test_electron_environment():
    """测试Electron环境"""
    print("🔍 测试Electron环境")
    print("=" * 50)
    
    try:
        # 检查Node.js
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js未安装")
            return False
        
        # 检查npm
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm: {result.stdout.strip()}")
        else:
            print("❌ npm未安装")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 环境检查失败: {e}")
        return False

def test_electron_files():
    """测试Electron文件结构"""
    print(f"\n📁 测试Electron文件结构")
    print("=" * 40)
    
    try:
        electron_dir = Path("electron")
        
        # 检查必要文件
        required_files = [
            "package.json",
            "main.js", 
            "preload.js",
            "index.html",
            "build.bat",
            "dev.bat",
            "README.md"
        ]
        
        print("📋 检查必要文件:")
        all_files_exist = True
        
        for file_name in required_files:
            file_path = electron_dir / file_name
            if file_path.exists():
                print(f"✅ {file_name}: 存在")
            else:
                print(f"❌ {file_name}: 缺失")
                all_files_exist = False
        
        # 检查package.json内容
        package_json_path = electron_dir / "package.json"
        if package_json_path.exists():
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
            
            print(f"\n📋 package.json配置:")
            print(f"  • 应用名称: {package_data.get('name', 'N/A')}")
            print(f"  • 产品名称: {package_data.get('build', {}).get('productName', 'N/A')}")
            print(f"  • 版本: {package_data.get('version', 'N/A')}")
            print(f"  • 作者: {package_data.get('author', 'N/A')}")
            
            # 检查构建配置
            build_config = package_data.get('build', {})
            if build_config:
                print(f"  • 应用ID: {build_config.get('appId', 'N/A')}")
                print(f"  • 目标平台: {build_config.get('win', {}).get('target', 'N/A')}")
        
        # 检查node_modules
        node_modules_path = electron_dir / "node_modules"
        if node_modules_path.exists():
            print(f"\n✅ node_modules: 已安装")
        else:
            print(f"\n⚠️ node_modules: 未安装，需要运行npm install")
        
        return all_files_exist
        
    except Exception as e:
        print(f"❌ 文件检查失败: {e}")
        return False

def test_python_integration():
    """测试Python集成"""
    print(f"\n🐍 测试Python集成")
    print("=" * 30)
    
    try:
        # 检查main.py
        main_py_path = Path("main.py")
        if main_py_path.exists():
            print("✅ main.py: 存在")
        else:
            print("❌ main.py: 缺失")
            return False
        
        # 检查requirements.txt
        requirements_path = Path("requirements.txt")
        if requirements_path.exists():
            print("✅ requirements.txt: 存在")
        else:
            print("❌ requirements.txt: 缺失")
            return False
        
        # 检查src目录
        src_path = Path("src")
        if src_path.exists():
            print("✅ src目录: 存在")
            
            # 检查关键模块
            key_modules = [
                "ai_model_processor.py",
                "pdf_processor.py", 
                "data_processor.py",
                "config.py"
            ]
            
            for module in key_modules:
                module_path = src_path / module
                if module_path.exists():
                    print(f"  ✅ {module}: 存在")
                else:
                    print(f"  ❌ {module}: 缺失")
        else:
            print("❌ src目录: 缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Python集成检查失败: {e}")
        return False

def test_build_capability():
    """测试构建能力"""
    print(f"\n🏗️ 测试构建能力")
    print("=" * 30)
    
    try:
        electron_dir = Path("electron")
        
        # 检查是否可以运行构建命令
        if not (electron_dir / "node_modules").exists():
            print("⚠️ 需要先安装依赖: npm install")
            return False
        
        # 检查构建脚本
        build_script = electron_dir / "build.bat"
        if build_script.exists():
            print("✅ 构建脚本: 存在")
        else:
            print("❌ 构建脚本: 缺失")
            return False
        
        # 检查开发脚本
        dev_script = electron_dir / "dev.bat"
        if dev_script.exists():
            print("✅ 开发脚本: 存在")
        else:
            print("❌ 开发脚本: 缺失")
            return False
        
        print(f"\n📋 构建选项:")
        print(f"  • 开发模式: electron/dev.bat")
        print(f"  • 构建安装程序: electron/build.bat")
        print(f"  • 手动构建: cd electron && npm run build-win")
        
        return True
        
    except Exception as e:
        print(f"❌ 构建能力检查失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print(f"\n📋 使用说明")
    print("=" * 30)
    
    print(f"🚀 快速开始:")
    print(f"  1. 进入electron目录: cd electron")
    print(f"  2. 安装依赖: npm install")
    print(f"  3. 开发模式: npm start 或 dev.bat")
    print(f"  4. 构建应用: npm run build-win 或 build.bat")
    
    print(f"\n📁 输出文件:")
    print(f"  • 安装程序: electron/dist/*.exe")
    print(f"  • 便携版: electron/dist/*-portable.exe")
    
    print(f"\n🎯 应用特性:")
    print(f"  • 原生Windows桌面应用")
    print(f"  • 自动启动Python后端")
    print(f"  • 专业安装程序")
    print(f"  • 完整菜单系统")
    print(f"  • 文件关联支持")
    
    print(f"\n📞 技术支持:")
    print(f"  • 开发者: Always派智能研究工作室")
    print(f"  • 邮箱: <EMAIL>")

def main():
    """主函数"""
    print("🔧 Electron打包系统测试")
    print("=" * 60)
    
    # 运行各项测试
    tests = [
        ("Electron环境", test_electron_environment),
        ("文件结构", test_electron_files),
        ("Python集成", test_python_integration),
        ("构建能力", test_build_capability)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        results[test_name] = test_func()
    
    # 汇总结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print(f"\n🎉 Electron打包系统准备就绪！")
        print(f"💡 主要功能:")
        print(f"   • 完整的Electron桌面应用框架")
        print(f"   • Python后端自动集成")
        print(f"   • 专业的Windows安装程序")
        print(f"   • 一键构建和部署")
        print(f"   • 完整的用户体验设计")
        
        show_usage_instructions()
        
    else:
        print(f"\n⚠️ 部分功能需要完善")
        
        if not results.get("Electron环境"):
            print(f"💡 请先安装Node.js: https://nodejs.org/")
        
        if not results.get("文件结构"):
            print(f"💡 请检查Electron文件是否完整")
        
        if results.get("Electron环境") and not (Path("electron") / "node_modules").exists():
            print(f"💡 请运行: cd electron && npm install")

if __name__ == "__main__":
    main()
