#!/usr/bin/env python3
"""
测试LM Studio集成功能
"""

import requests
from src.ai_model_processor import AIModelProcessor

def test_lm_studio_detection():
    """测试LM Studio检测功能"""
    
    print("🧪 测试LM Studio集成功能")
    print("=" * 60)
    
    # 创建AI处理器
    processor = AIModelProcessor()
    
    # 检查LM Studio是否可用
    lm_studio_available = processor._check_lm_studio_available()
    
    print(f"📊 LM Studio状态检查:")
    print(f"- 服务地址: http://127.0.0.1:1234")
    print(f"- 服务状态: {'✅ 运行中' if lm_studio_available else '❌ 未运行'}")
    
    if lm_studio_available:
        # 获取可用模型
        lm_studio_models = processor.get_lm_studio_models()
        print(f"- 可用模型数量: {len(lm_studio_models)}")
        
        if lm_studio_models:
            print(f"- 模型列表:")
            for model in lm_studio_models:
                print(f"  • {model}")
                
            # 检查是否有monkeyocr-recognition模型
            has_monkeyocr = "monkeyocr-recognition" in lm_studio_models
            print(f"- monkeyocr-recognition模型: {'✅ 已加载' if has_monkeyocr else '❌ 未加载'}")
        else:
            print(f"- ⚠️ LM Studio运行中但没有加载模型")
    else:
        print(f"- 💡 请启动LM Studio并加载模型")
    
    # 获取所有可用模型（包括LM Studio）
    all_models = processor.get_available_models()
    lm_studio_in_list = any(key.startswith("lm_studio") for key in all_models.keys())
    
    print(f"\n🔍 模型列表集成检查:")
    print(f"- LM Studio模型已集成: {'✅' if lm_studio_in_list else '❌'}")
    
    if lm_studio_in_list:
        lm_models = {k: v for k, v in all_models.items() if k.startswith("lm_studio")}
        print(f"- 集成的LM Studio模型:")
        for key, name in lm_models.items():
            print(f"  • {key}: {name}")
    
    return lm_studio_available

def test_lm_studio_api():
    """测试LM Studio API调用"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试LM Studio API调用")
    print("=" * 60)
    
    try:
        # 测试模型列表API
        print("📋 测试模型列表API:")
        response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
        
        if response.status_code == 200:
            print("✅ 模型列表API响应正常")
            data = response.json()
            models = [model["id"] for model in data.get("data", [])]
            print(f"- 返回模型数量: {len(models)}")
            for model in models:
                print(f"  • {model}")
        else:
            print(f"❌ 模型列表API错误: {response.status_code}")
            return False
        
        # 测试聊天API（如果有模型）
        if models:
            print(f"\n💬 测试聊天API:")
            test_model = models[0]
            print(f"- 使用模型: {test_model}")
            
            chat_data = {
                "model": test_model,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello, this is a test message."
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }
            
            chat_response = requests.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                json=chat_data,
                timeout=30
            )
            
            if chat_response.status_code == 200:
                print("✅ 聊天API响应正常")
                result = chat_response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"- 响应内容: {content[:100]}...")
            else:
                print(f"❌ 聊天API错误: {chat_response.status_code}")
                print(f"- 错误信息: {chat_response.text[:200]}")
                return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到LM Studio服务")
        print("💡 请确保LM Studio正在运行并监听端口1234")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def provide_lm_studio_setup_guide():
    """提供LM Studio设置指南"""
    
    print(f"\n" + "=" * 60)
    print("📖 LM Studio设置指南")
    print("=" * 60)
    
    print("🚀 启动LM Studio:")
    print("1. 打开LM Studio应用程序")
    print("2. 在'Chat'标签页中选择或下载模型")
    print("3. 点击'Start Server'按钮启动本地服务器")
    print("4. 确保服务器地址为: http://127.0.0.1:1234")
    
    print(f"\n🤖 推荐模型:")
    print("- monkeyocr-recognition (OCR专用)")
    print("- llava-v1.6-mistral-7b (视觉理解)")
    print("- qwen2-vl-7b-instruct (中文视觉)")
    
    print(f"\n⚙️ 配置说明:")
    print("- 模型名称: monkeyocr-recognition")
    print("- 服务地址: http://127.0.0.1:1234")
    print("- API格式: OpenAI兼容")
    
    print(f"\n🔧 故障排除:")
    print("- 确保LM Studio服务器已启动")
    print("- 检查端口1234是否被占用")
    print("- 确保模型已正确加载")
    print("- 检查防火墙设置")

if __name__ == "__main__":
    print("🚀 开始测试LM Studio集成功能")
    print("=" * 80)
    
    # 测试LM Studio检测
    lm_studio_available = test_lm_studio_detection()
    
    # 测试API调用
    if lm_studio_available:
        api_working = test_lm_studio_api()
    else:
        api_working = False
    
    # 提供设置指南
    provide_lm_studio_setup_guide()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- LM Studio服务: {'✅ 运行中' if lm_studio_available else '❌ 未运行'}")
    print(f"- API功能: {'✅ 正常' if api_working else '❌ 异常'}")
    
    if lm_studio_available and api_working:
        print("🎉 LM Studio集成成功！")
        print("📋 现在可以在系统中使用LM Studio模型了。")
    else:
        print("⚠️ LM Studio集成需要配置:")
        print("1. 启动LM Studio应用")
        print("2. 加载monkeyocr-recognition模型")
        print("3. 启动本地服务器")
        print("4. 在系统中刷新模型列表")
