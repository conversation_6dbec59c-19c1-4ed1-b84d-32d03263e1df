#!/usr/bin/env python3
"""
测试模型配置修复和自定义OpenAI模型
"""

from src.ai_model_processor import AIModelProcessor

def test_model_detection():
    """测试模型检测功能"""
    
    print("🧪 测试模型检测功能")
    print("=" * 60)
    
    # 创建AI处理器
    processor = AIModelProcessor()
    
    # 获取可用模型
    available_models = processor.get_available_models()
    
    print(f"📊 检测结果:")
    print(f"- 可用模型数量: {len(available_models)}")
    
    if available_models:
        print(f"- 可用模型列表:")
        
        # 分类显示模型
        api_models = {}
        ollama_models = {}
        lm_studio_models = {}
        custom_models = {}
        
        for model_id, model_name in available_models.items():
            if model_id.startswith("ollama_"):
                ollama_models[model_id] = model_name
            elif model_id.startswith("lm_studio_"):
                lm_studio_models[model_id] = model_name
            elif model_id == "custom_openai":
                custom_models[model_id] = model_name
            else:
                api_models[model_id] = model_name
        
        if api_models:
            print(f"\n  🌐 API模型 ({len(api_models)} 个):")
            for key, name in api_models.items():
                print(f"    • {key}: {name}")
        
        if ollama_models:
            print(f"\n  🦙 Ollama模型 ({len(ollama_models)} 个):")
            for key, name in list(ollama_models.items())[:5]:  # 只显示前5个
                print(f"    • {key}: {name}")
            if len(ollama_models) > 5:
                print(f"    ... 还有 {len(ollama_models) - 5} 个模型")
        
        if lm_studio_models:
            print(f"\n  🏠 LM Studio模型 ({len(lm_studio_models)} 个):")
            for key, name in list(lm_studio_models.items())[:5]:  # 只显示前5个
                print(f"    • {key}: {name}")
            if len(lm_studio_models) > 5:
                print(f"    ... 还有 {len(lm_studio_models) - 5} 个模型")
        
        if custom_models:
            print(f"\n  🔧 自定义模型 ({len(custom_models)} 个):")
            for key, name in custom_models.items():
                print(f"    • {key}: {name}")
        
        if not any([api_models, ollama_models, lm_studio_models, custom_models]):
            print("  ❌ 没有检测到任何模型")
    else:
        print(f"- ❌ 没有检测到可用模型")
    
    return len(available_models) > 0

def test_custom_openai_config():
    """测试自定义OpenAI模型配置"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试自定义OpenAI模型配置")
    print("=" * 60)
    
    import os
    
    # 检查当前配置
    api_key = os.environ.get("CUSTOM_OPENAI_API_KEY", "")
    api_url = os.environ.get("CUSTOM_OPENAI_API_URL", "")
    model_name = os.environ.get("CUSTOM_OPENAI_MODEL_NAME", "")
    
    print(f"📋 当前配置状态:")
    print(f"- API密钥: {'✅ 已配置' if api_key else '❌ 未配置'}")
    print(f"- API端点: {'✅ 已配置' if api_url else '❌ 未配置'} ({api_url})")
    print(f"- 模型名称: {'✅ 已配置' if model_name else '❌ 未配置'} ({model_name})")
    
    # 检查是否在可用模型列表中
    processor = AIModelProcessor()
    available_models = processor.get_available_models()
    
    has_custom_openai = "custom_openai" in available_models
    print(f"- 在模型列表中: {'✅ 是' if has_custom_openai else '❌ 否'}")
    
    if has_custom_openai:
        print(f"- 显示名称: {available_models['custom_openai']}")
    
    print(f"\n💡 配置示例:")
    print(f"- OpenAI官方: https://api.openai.com/v1")
    print(f"- DeepSeek: https://api.deepseek.com/v1")
    print(f"- 本地服务: http://localhost:8000/v1")
    print(f"- LM Studio: http://127.0.0.1:1234/v1")
    
    return has_custom_openai

def test_model_dropdown_choices():
    """测试模型下拉菜单选项"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试模型下拉菜单选项")
    print("=" * 60)
    
    # 创建AI处理器
    processor = AIModelProcessor()
    available_models = processor.get_available_models()
    
    # 模拟下拉菜单的choices格式
    choices = list(available_models.items())
    
    print(f"📋 下拉菜单选项 ({len(choices)} 个):")
    
    # 分类显示
    api_choices = []
    local_choices = []
    
    for key, display_name in choices:
        if key.startswith(("ollama_", "lm_studio_", "custom_openai")):
            local_choices.append((key, display_name))
        else:
            api_choices.append((key, display_name))
    
    if api_choices:
        print(f"\n  🌐 API模型选项:")
        for key, name in api_choices:
            print(f"    • {key} → {name}")
    
    if local_choices:
        print(f"\n  🏠 本地模型选项 (显示前10个):")
        for key, name in local_choices[:10]:
            print(f"    • {key} → {name}")
        if len(local_choices) > 10:
            print(f"    ... 还有 {len(local_choices) - 10} 个选项")
    
    # 检查是否有重复的键值
    keys = [key for key, _ in choices]
    unique_keys = set(keys)
    
    print(f"\n🔍 选项检查:")
    print(f"- 总选项数: {len(choices)}")
    print(f"- 唯一键值数: {len(unique_keys)}")
    print(f"- 是否有重复: {'❌ 有重复' if len(keys) != len(unique_keys) else '✅ 无重复'}")
    
    return len(choices) > 0

def provide_configuration_guide():
    """提供配置指南"""
    
    print(f"\n" + "=" * 60)
    print("📖 模型配置指南")
    print("=" * 60)
    
    print("🔧 修复的问题:")
    print("1. ✅ 模型下拉菜单现在显示所有类型的模型")
    print("2. ✅ 模型配置页面正常工作")
    print("3. ✅ 新增自定义OpenAI兼容模型配置")
    
    print(f"\n⚙️ 配置自定义OpenAI模型:")
    print("1. 点击'⚙️ 配置模型'按钮")
    print("2. 选择'自定义OpenAI兼容模型'")
    print("3. 填写以下信息:")
    print("   - API密钥: 你的API密钥")
    print("   - API端点URL: 如 https://api.openai.com/v1")
    print("   - 模型名称: 如 gpt-4-vision-preview")
    print("4. 点击'💾 保存配置'")
    
    print(f"\n🎯 推荐配置:")
    print("- 🥇 LM Studio: monkeyocr-recognition (专业OCR)")
    print("- 🥈 阿里通义千问-QVQ-Max (高精度API)")
    print("- 🥉 自定义OpenAI: 灵活配置第三方服务")
    print("- 🏅 Ollama: qwen2.5vl:7b (免费本地)")

if __name__ == "__main__":
    print("🚀 开始测试模型配置修复")
    print("=" * 80)
    
    # 测试模型检测
    detection_success = test_model_detection()
    
    # 测试自定义OpenAI配置
    custom_openai_success = test_custom_openai_config()
    
    # 测试下拉菜单选项
    dropdown_success = test_model_dropdown_choices()
    
    # 提供配置指南
    provide_configuration_guide()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- 模型检测: {'✅ 正常' if detection_success else '❌ 异常'}")
    print(f"- 自定义OpenAI: {'✅ 已配置' if custom_openai_success else '⚠️ 未配置'}")
    print(f"- 下拉菜单: {'✅ 正常' if dropdown_success else '❌ 异常'}")
    
    if detection_success and dropdown_success:
        print("🎉 模型配置修复成功！")
        print("📋 现在可以:")
        print("  1. 在下拉菜单中看到所有类型的模型")
        print("  2. 正常使用模型配置页面")
        print("  3. 配置自定义OpenAI兼容模型")
    else:
        print("⚠️ 部分功能需要进一步检查。")
