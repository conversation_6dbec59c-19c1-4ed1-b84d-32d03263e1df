#!/usr/bin/env python3
"""
验证版权信息是否正确添加
"""

def verify_copyright_info():
    """验证版权信息"""
    print("🔍 验证版权信息添加")
    print("=" * 50)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查版权相关内容
        copyright_elements = [
            {
                "text": "Always派智能研究工作室版权所有",
                "description": "版权声明"
            },
            {
                "text": "<EMAIL>",
                "description": "联系邮箱"
            },
            {
                "text": "© 2024",
                "description": "版权年份"
            },
            {
                "text": "联系方式：",
                "description": "联系方式标签"
            },
            {
                "text": "mailto:<EMAIL>",
                "description": "邮箱链接"
            }
        ]
        
        print("📋 检查版权信息元素:")
        all_found = True
        
        for element in copyright_elements:
            if element["text"] in content:
                print(f"✅ {element['description']}: 已添加")
            else:
                print(f"❌ {element['description']}: 未找到")
                all_found = False
        
        # 检查HTML结构
        html_elements = [
            "<!-- 版权信息 -->",
            "background: linear-gradient",
            "color: #888",
            "color: #999",
            "color: #667eea"
        ]
        
        print(f"\n📋 检查HTML结构:")
        for element in html_elements:
            if element in content:
                print(f"✅ {element}: 已包含")
            else:
                print(f"❌ {element}: 未包含")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_system_running():
    """验证系统是否正常运行"""
    print(f"\n🚀 验证系统运行状态")
    print("=" * 40)
    
    try:
        import requests
        
        try:
            response = requests.get("http://0.0.0.0:7863", timeout=10)
            if response.status_code == 200:
                print("✅ 系统运行正常")
                
                # 检查页面内容是否包含版权信息
                if "Always派智能研究工作室版权所有" in response.text:
                    print("✅ 页面显示版权信息")
                    return True
                else:
                    print("⚠️ 页面未显示版权信息")
                    return False
            else:
                print(f"⚠️ 系统响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("⚠️ 无法连接到系统")
            return False
        except Exception as e:
            print(f"⚠️ 连接测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_copyright_position():
    """检查版权信息的位置"""
    print(f"\n📍 检查版权信息位置")
    print("=" * 40)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 查找版权信息的行号
        copyright_start = None
        copyright_end = None
        
        for i, line in enumerate(lines):
            if "<!-- 版权信息 -->" in line:
                copyright_start = i + 1
            if copyright_start and "</div>" in line and "版权" in lines[i-5:i+1]:
                copyright_end = i + 1
                break
        
        if copyright_start and copyright_end:
            print(f"✅ 版权信息位置: 第 {copyright_start} - {copyright_end} 行")
            
            # 显示版权信息内容
            print(f"\n📄 版权信息内容:")
            for i in range(copyright_start-1, min(copyright_end, len(lines))):
                line_content = lines[i].strip()
                if line_content and not line_content.startswith("<!--"):
                    print(f"   {line_content}")
            
            return True
        else:
            print("❌ 未找到版权信息位置")
            return False
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 版权信息添加验证")
    print("=" * 60)
    
    # 验证版权信息
    copyright_check = verify_copyright_info()
    
    # 检查版权位置
    position_check = check_copyright_position()
    
    # 验证系统运行
    system_check = verify_system_running()
    
    print(f"\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print(f"   📝 版权信息: {'✅ 正确' if copyright_check else '❌ 有问题'}")
    print(f"   📍 信息位置: {'✅ 正确' if position_check else '❌ 有问题'}")
    print(f"   🚀 系统运行: {'✅ 正常' if system_check else '❌ 异常'}")
    
    if copyright_check and position_check and system_check:
        print(f"\n🎉 版权信息添加完成！")
        print(f"💡 版权内容:")
        print(f"   • 版权所有：Always派智能研究工作室")
        print(f"   • 版权年份：© 2024")
        print(f"   • 联系方式：<EMAIL>")
        print(f"   • 邮箱链接：可点击发送邮件")
        print(f"\n🌐 访问地址: http://0.0.0.0:7863")
        print(f"📍 版权信息显示在页面底部")
    else:
        print(f"\n⚠️ 部分内容需要检查")

if __name__ == "__main__":
    main()
